"""
Agent response models for AI Terminal.

Defines the structure and handling of AI agent responses including
content, tool calls, metadata, and streaming support.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional

from ai_terminal.core.logger import LoggerMixin


@dataclass
class ToolCall:
    """Represents a tool call made by the AI agent."""
    
    id: str
    name: str
    args: Dict[str, Any]
    result: Optional[Any] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "id": self.id,
            "name": self.name,
            "args": self.args,
            "result": self.result,
            "error": self.error,
            "execution_time": self.execution_time,
            "timestamp": self.timestamp.isoformat(),
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ToolCall":
        """Create from dictionary representation."""
        timestamp = datetime.fromisoformat(data["timestamp"]) if "timestamp" in data else datetime.now()
        return cls(
            id=data["id"],
            name=data["name"],
            args=data["args"],
            result=data.get("result"),
            error=data.get("error"),
            execution_time=data.get("execution_time"),
            timestamp=timestamp,
        )


@dataclass
class AgentResponse:
    """Represents a complete response from the AI agent."""
    
    content: str
    model: str
    provider: str
    tool_calls: List[ToolCall] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Token usage information
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    
    # Response timing
    response_time: Optional[float] = None
    first_token_time: Optional[float] = None
    
    # Streaming support
    is_streaming: bool = False
    stream_finished: bool = False
    
    def add_tool_call(self, tool_call: ToolCall) -> None:
        """Add a tool call to the response."""
        self.tool_calls.append(tool_call)
    
    def get_tool_call_by_id(self, tool_id: str) -> Optional[ToolCall]:
        """Get a tool call by its ID."""
        for tool_call in self.tool_calls:
            if tool_call.id == tool_id:
                return tool_call
        return None
    
    def has_tool_calls(self) -> bool:
        """Check if response has any tool calls."""
        return len(self.tool_calls) > 0
    
    def get_successful_tool_calls(self) -> List[ToolCall]:
        """Get all successful tool calls."""
        return [tc for tc in self.tool_calls if tc.error is None]
    
    def get_failed_tool_calls(self) -> List[ToolCall]:
        """Get all failed tool calls."""
        return [tc for tc in self.tool_calls if tc.error is not None]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "content": self.content,
            "model": self.model,
            "provider": self.provider,
            "tool_calls": [tc.to_dict() for tc in self.tool_calls],
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat(),
            "prompt_tokens": self.prompt_tokens,
            "completion_tokens": self.completion_tokens,
            "total_tokens": self.total_tokens,
            "response_time": self.response_time,
            "first_token_time": self.first_token_time,
            "is_streaming": self.is_streaming,
            "stream_finished": self.stream_finished,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AgentResponse":
        """Create from dictionary representation."""
        timestamp = datetime.fromisoformat(data["timestamp"]) if "timestamp" in data else datetime.now()
        
        tool_calls = []
        for tc_data in data.get("tool_calls", []):
            tool_calls.append(ToolCall.from_dict(tc_data))
        
        return cls(
            content=data["content"],
            model=data["model"],
            provider=data["provider"],
            tool_calls=tool_calls,
            metadata=data.get("metadata", {}),
            timestamp=timestamp,
            prompt_tokens=data.get("prompt_tokens", 0),
            completion_tokens=data.get("completion_tokens", 0),
            total_tokens=data.get("total_tokens", 0),
            response_time=data.get("response_time"),
            first_token_time=data.get("first_token_time"),
            is_streaming=data.get("is_streaming", False),
            stream_finished=data.get("stream_finished", False),
        )


class StreamingResponse(LoggerMixin):
    """Handles streaming responses from AI providers."""
    
    def __init__(self, model: str, provider: str):
        """Initialize streaming response."""
        self.model = model
        self.provider = provider
        self.content_buffer = ""
        self.tool_calls: List[ToolCall] = []
        self.metadata: Dict[str, Any] = {}
        self.start_time = datetime.now()
        self.first_token_time: Optional[float] = None
        self.is_finished = False
        
        # Token tracking
        self.prompt_tokens = 0
        self.completion_tokens = 0
        
        # Callbacks
        self.on_token_callback: Optional[callable] = None
        self.on_tool_call_callback: Optional[callable] = None
        self.on_finished_callback: Optional[callable] = None
    
    def add_token(self, token: str) -> None:
        """Add a token to the streaming response."""
        if not self.first_token_time:
            self.first_token_time = (datetime.now() - self.start_time).total_seconds()
        
        self.content_buffer += token
        self.completion_tokens += 1
        
        if self.on_token_callback:
            try:
                self.on_token_callback(token)
            except Exception as e:
                self.logger.error(f"Error in token callback: {e}")
    
    def add_tool_call(self, tool_call: ToolCall) -> None:
        """Add a tool call to the streaming response."""
        self.tool_calls.append(tool_call)
        
        if self.on_tool_call_callback:
            try:
                self.on_tool_call_callback(tool_call)
            except Exception as e:
                self.logger.error(f"Error in tool call callback: {e}")
    
    def finish(self) -> AgentResponse:
        """Finish streaming and return final response."""
        if self.is_finished:
            return self.to_agent_response()
        
        self.is_finished = True
        end_time = datetime.now()
        response_time = (end_time - self.start_time).total_seconds()
        
        response = AgentResponse(
            content=self.content_buffer,
            model=self.model,
            provider=self.provider,
            tool_calls=self.tool_calls,
            metadata=self.metadata,
            timestamp=self.start_time,
            prompt_tokens=self.prompt_tokens,
            completion_tokens=self.completion_tokens,
            total_tokens=self.prompt_tokens + self.completion_tokens,
            response_time=response_time,
            first_token_time=self.first_token_time,
            is_streaming=False,
            stream_finished=True,
        )
        
        if self.on_finished_callback:
            try:
                self.on_finished_callback(response)
            except Exception as e:
                self.logger.error(f"Error in finished callback: {e}")
        
        return response
    
    def to_agent_response(self) -> AgentResponse:
        """Convert current state to AgentResponse."""
        return AgentResponse(
            content=self.content_buffer,
            model=self.model,
            provider=self.provider,
            tool_calls=self.tool_calls.copy(),
            metadata=self.metadata.copy(),
            timestamp=self.start_time,
            prompt_tokens=self.prompt_tokens,
            completion_tokens=self.completion_tokens,
            total_tokens=self.prompt_tokens + self.completion_tokens,
            response_time=None if not self.is_finished else (datetime.now() - self.start_time).total_seconds(),
            first_token_time=self.first_token_time,
            is_streaming=not self.is_finished,
            stream_finished=self.is_finished,
        )
    
    def set_token_callback(self, callback: callable) -> None:
        """Set callback for token updates."""
        self.on_token_callback = callback
    
    def set_tool_call_callback(self, callback: callable) -> None:
        """Set callback for tool calls."""
        self.on_tool_call_callback = callback
    
    def set_finished_callback(self, callback: callable) -> None:
        """Set callback for completion."""
        self.on_finished_callback = callback
