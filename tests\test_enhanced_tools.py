"""
Test suite for enhanced tool implementations.

Tests the completed Git tool and Package Manager tool implementations
to ensure proper functionality and error handling.
"""

import asyncio
import subprocess
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from ai_terminal.core.config import Config
from ai_terminal.tools.git import GitTool
from ai_terminal.tools.package_manager import PackageManagerTool


@pytest.fixture
def mock_config():
    """Create a mock configuration."""
    config = MagicMock(spec=Config)
    config.get.return_value = True
    return config


@pytest.fixture
def git_tool():
    """Create a Git tool instance."""
    return GitTool()


@pytest.fixture
def package_tool():
    """Create a Package Manager tool instance."""
    return PackageManagerTool()


class TestGitTool:
    """Test the enhanced Git tool implementation."""
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_git_branch_list(self, mock_run, git_tool):
        """Test git branch listing."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="* main\n  feature-branch\n  remotes/origin/main\n",
            stderr=""
        )
        
        result = await git_tool._git_branch()
        
        assert result["success"] is True
        assert "main" in result["branches"]
        assert "feature-branch" in result["branches"]
        assert result["current"] == "main"
        mock_run.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_git_branch_create(self, mock_run, git_tool):
        """Test git branch creation."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="",
            stderr=""
        )
        
        result = await git_tool._git_branch("new-feature")
        
        assert result["success"] is True
        assert result["branch"] == "new-feature"
        assert result["created"] is True
        mock_run.assert_called_once_with(
            ["git", "branch", "new-feature"],
            capture_output=True,
            text=True,
            timeout=30
        )
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_git_branch_create_and_checkout(self, mock_run, git_tool):
        """Test git branch creation with checkout."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="",
            stderr=""
        )
        
        result = await git_tool._git_branch("new-feature", checkout=True)
        
        assert result["success"] is True
        assert result["branch"] == "new-feature"
        assert result["created"] is True
        assert result["checked_out"] is True
        mock_run.assert_called_once_with(
            ["git", "checkout", "-b", "new-feature"],
            capture_output=True,
            text=True,
            timeout=30
        )
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_git_checkout_existing_branch(self, mock_run, git_tool):
        """Test git checkout of existing branch."""
        # Mock branch list check
        mock_run.side_effect = [
            MagicMock(returncode=0, stdout="feature-branch\n", stderr=""),  # branch exists
            MagicMock(returncode=0, stdout="", stderr="")  # checkout success
        ]
        
        result = await git_tool._git_checkout("feature-branch")
        
        assert result["success"] is True
        assert result["branch"] == "feature-branch"
        assert result["checked_out"] is True
        assert result["created_from_remote"] is False
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_git_checkout_remote_branch(self, mock_run, git_tool):
        """Test git checkout of remote branch."""
        # Mock branch list check (empty) and checkout from remote
        mock_run.side_effect = [
            MagicMock(returncode=0, stdout="", stderr=""),  # branch doesn't exist locally
            MagicMock(returncode=0, stdout="", stderr="")  # checkout from remote success
        ]
        
        result = await git_tool._git_checkout("remote-feature")
        
        assert result["success"] is True
        assert result["branch"] == "remote-feature"
        assert result["checked_out"] is True
        assert result["created_from_remote"] is True
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_git_log(self, mock_run, git_tool):
        """Test git log functionality."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="abc123|John Doe|<EMAIL>|2023-01-01 12:00:00|Initial commit\n"
                   "def456|Jane Smith|<EMAIL>|2023-01-02 13:00:00|Add feature\n",
            stderr=""
        )
        
        result = await git_tool._git_log(limit=2)
        
        assert result["success"] is True
        assert len(result["commits"]) == 2
        assert result["commits"][0]["hash"] == "abc123"
        assert result["commits"][0]["author"] == "John Doe"
        assert result["commits"][0]["message"] == "Initial commit"
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_git_diff(self, mock_run, git_tool):
        """Test git diff functionality."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="diff --git a/file.txt b/file.txt\n+added line\n-removed line\n",
            stderr=""
        )
        
        result = await git_tool._git_diff("file.txt")
        
        assert result["success"] is True
        assert result["file"] == "file.txt"
        assert result["has_changes"] is True
        assert "added line" in result["diff"]
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_git_clone(self, mock_run, git_tool):
        """Test git clone functionality."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="",
            stderr=""
        )
        
        with patch('os.path.exists', return_value=False):
            result = await git_tool._git_clone("https://github.com/user/repo.git", "my-repo")
        
        assert result["success"] is True
        assert result["url"] == "https://github.com/user/repo.git"
        assert result["directory"] == "my-repo"
        assert result["cloned"] is True
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_git_error_handling(self, mock_run, git_tool):
        """Test git error handling."""
        mock_run.return_value = MagicMock(
            returncode=1,
            stdout="",
            stderr="fatal: not a git repository"
        )
        
        result = await git_tool._git_branch()
        
        assert result["success"] is False
        assert "fatal: not a git repository" in result["error"]


class TestPackageManagerTool:
    """Test the enhanced Package Manager tool implementation."""
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_npm_install(self, mock_run, package_tool):
        """Test npm package installation."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="+ package@1.0.0\nadded 1 package\n",
            stderr=""
        )
        
        result = await package_tool._npm_operation("install", package="express", version="4.18.0")
        
        assert result["success"] is True
        assert result["manager"] == "npm"
        assert result["operation"] == "install"
        assert result["package"] == "express"
        mock_run.assert_called_once_with(
            ["npm", "install", "express@4.18.0"],
            capture_output=True,
            text=True,
            timeout=120
        )
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_npm_install_global(self, mock_run, package_tool):
        """Test npm global package installation."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="+ package@1.0.0\n",
            stderr=""
        )
        
        result = await package_tool._npm_operation("install", package="typescript", **{"global": True})
        
        assert result["success"] is True
        mock_run.assert_called_once_with(
            ["npm", "install", "typescript", "--global"],
            capture_output=True,
            text=True,
            timeout=120
        )
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_npm_uninstall(self, mock_run, package_tool):
        """Test npm package uninstallation."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="removed 1 package\n",
            stderr=""
        )
        
        result = await package_tool._npm_operation("uninstall", package="express")
        
        assert result["success"] is True
        mock_run.assert_called_once_with(
            ["npm", "uninstall", "express"],
            capture_output=True,
            text=True,
            timeout=120
        )
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_cargo_install(self, mock_run, package_tool):
        """Test cargo package installation."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="Installing package...\nInstalled package\n",
            stderr=""
        )
        
        result = await package_tool._cargo_operation("install", package="ripgrep")
        
        assert result["success"] is True
        assert result["manager"] == "cargo"
        assert result["package"] == "ripgrep"
        mock_run.assert_called_once_with(
            ["cargo", "install", "ripgrep"],
            capture_output=True,
            text=True,
            timeout=300
        )
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_cargo_search(self, mock_run, package_tool):
        """Test cargo package search."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="ripgrep = \"13.0.0\"    # A line-oriented search tool\n",
            stderr=""
        )
        
        result = await package_tool._cargo_operation("search", package="ripgrep")
        
        assert result["success"] is True
        mock_run.assert_called_once_with(
            ["cargo", "search", "ripgrep"],
            capture_output=True,
            text=True,
            timeout=300
        )
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_apt_install(self, mock_run, package_tool):
        """Test apt package installation."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="Reading package lists...\nInstalling package...\n",
            stderr=""
        )
        
        result = await package_tool._apt_operation("install", package="curl")
        
        assert result["success"] is True
        assert result["manager"] == "apt"
        mock_run.assert_called_once_with(
            ["sudo", "apt", "install", "-y", "curl"],
            capture_output=True,
            text=True,
            timeout=300
        )
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_brew_install(self, mock_run, package_tool):
        """Test brew package installation."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="Installing package...\nInstalled package\n",
            stderr=""
        )
        
        result = await package_tool._brew_operation("install", package="wget")
        
        assert result["success"] is True
        assert result["manager"] == "brew"
        mock_run.assert_called_once_with(
            ["brew", "install", "wget"],
            capture_output=True,
            text=True,
            timeout=300
        )
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_brew_install_cask(self, mock_run, package_tool):
        """Test brew cask installation."""
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="Installing cask...\nInstalled cask\n",
            stderr=""
        )
        
        result = await package_tool._brew_operation("install", package="firefox", cask=True)
        
        assert result["success"] is True
        mock_run.assert_called_once_with(
            ["brew", "install", "--cask", "firefox"],
            capture_output=True,
            text=True,
            timeout=300
        )
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_package_manager_error_handling(self, mock_run, package_tool):
        """Test package manager error handling."""
        mock_run.return_value = MagicMock(
            returncode=1,
            stdout="",
            stderr="Package not found"
        )
        
        result = await package_tool._npm_operation("install", package="nonexistent-package")
        
        assert result["success"] is False
        assert "Package not found" in result["error"]
    
    @pytest.mark.asyncio
    async def test_missing_package_name(self, package_tool):
        """Test error handling for missing package name."""
        result = await package_tool._npm_operation("install")
        
        assert result["success"] is False
        assert "Package name required" in result["error"]
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_timeout_handling(self, mock_run, package_tool):
        """Test timeout handling."""
        mock_run.side_effect = subprocess.TimeoutExpired(["npm", "install"], 120)
        
        result = await package_tool._npm_operation("install", package="test-package")
        
        assert result["success"] is False
        assert "timed out" in result["error"]
