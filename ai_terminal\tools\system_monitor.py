"""
System monitoring tool for AI Terminal.

Provides system resource monitoring, process management,
and performance metrics collection.
"""

import psutil
from typing import Any, Dict, List

from ai_terminal.tools.base import BaseTool, ToolExecutionError


class SystemMonitorTool(BaseTool):
    """System monitoring and process management tool."""
    
    def __init__(self):
        """Initialize system monitor tool."""
        super().__init__(
            name="system_monitor",
            description="System resource monitoring and process management"
        )
        self.requires_approval = False
    
    async def execute(self, operation: str, **kwargs) -> Any:
        """Execute a system monitoring operation."""
        operations = {
            "cpu": self._get_cpu_info,
            "memory": self._get_memory_info,
            "disk": self._get_disk_info,
            "network": self._get_network_info,
            "processes": self._get_processes,
            "system": self._get_system_info,
        }
        
        if operation not in operations:
            raise ToolExecutionError(f"Unknown system operation: {operation}", self.name)
        
        return await operations[operation](**kwargs)
    
    async def _get_cpu_info(self, **kwargs) -> Dict[str, Any]:
        """Get CPU usage and information."""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            return {
                "usage_percent": cpu_percent,
                "core_count": cpu_count,
                "frequency": {
                    "current": cpu_freq.current if cpu_freq else None,
                    "min": cpu_freq.min if cpu_freq else None,
                    "max": cpu_freq.max if cpu_freq else None,
                },
                "per_core": psutil.cpu_percent(percpu=True),
            }
        except Exception as e:
            raise ToolExecutionError(f"Failed to get CPU info: {e}", self.name)
    
    async def _get_memory_info(self, **kwargs) -> Dict[str, Any]:
        """Get memory usage information."""
        try:
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            return {
                "virtual": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percent": memory.percent,
                },
                "swap": {
                    "total": swap.total,
                    "used": swap.used,
                    "free": swap.free,
                    "percent": swap.percent,
                }
            }
        except Exception as e:
            raise ToolExecutionError(f"Failed to get memory info: {e}", self.name)
    
    async def _get_disk_info(self, path: str = "/", **kwargs) -> Dict[str, Any]:
        """Get disk usage information."""
        try:
            disk_usage = psutil.disk_usage(path)
            disk_io = psutil.disk_io_counters()
            
            return {
                "usage": {
                    "total": disk_usage.total,
                    "used": disk_usage.used,
                    "free": disk_usage.free,
                    "percent": (disk_usage.used / disk_usage.total) * 100,
                },
                "io": {
                    "read_bytes": disk_io.read_bytes if disk_io else 0,
                    "write_bytes": disk_io.write_bytes if disk_io else 0,
                    "read_count": disk_io.read_count if disk_io else 0,
                    "write_count": disk_io.write_count if disk_io else 0,
                } if disk_io else None
            }
        except Exception as e:
            raise ToolExecutionError(f"Failed to get disk info: {e}", self.name)
    
    async def _get_network_info(self, **kwargs) -> Dict[str, Any]:
        """Get network usage information."""
        try:
            net_io = psutil.net_io_counters()
            net_connections = len(psutil.net_connections())
            
            return {
                "io": {
                    "bytes_sent": net_io.bytes_sent,
                    "bytes_recv": net_io.bytes_recv,
                    "packets_sent": net_io.packets_sent,
                    "packets_recv": net_io.packets_recv,
                },
                "connections": net_connections,
            }
        except Exception as e:
            raise ToolExecutionError(f"Failed to get network info: {e}", self.name)
    
    async def _get_processes(self, limit: int = 10, sort_by: str = "cpu", **kwargs) -> Dict[str, Any]:
        """Get running processes information."""
        try:
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    proc_info = proc.info
                    proc_info['cpu_percent'] = proc.cpu_percent()
                    processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Sort processes
            if sort_by == "cpu":
                processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
            elif sort_by == "memory":
                processes.sort(key=lambda x: x.get('memory_percent', 0), reverse=True)
            
            return {
                "processes": processes[:limit],
                "total_count": len(processes),
                "sorted_by": sort_by,
            }
        except Exception as e:
            raise ToolExecutionError(f"Failed to get processes: {e}", self.name)
    
    async def _get_system_info(self, **kwargs) -> Dict[str, Any]:
        """Get general system information."""
        try:
            boot_time = psutil.boot_time()
            users = psutil.users()
            
            return {
                "boot_time": boot_time,
                "uptime": psutil.time.time() - boot_time,
                "users": [
                    {
                        "name": user.name,
                        "terminal": user.terminal,
                        "host": user.host,
                        "started": user.started,
                    }
                    for user in users
                ],
                "platform": {
                    "system": psutil.WINDOWS if psutil.WINDOWS else "Unix",
                    "python_version": psutil.sys.version,
                }
            }
        except Exception as e:
            raise ToolExecutionError(f"Failed to get system info: {e}", self.name)
    
    def get_schema(self) -> Dict[str, Any]:
        """Get JSON schema for system monitor tool."""
        return {
            "name": "system_monitor",
            "description": "System resource monitoring and process management",
            "parameters": {
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": ["cpu", "memory", "disk", "network", "processes", "system"],
                        "description": "System monitoring operation to perform"
                    },
                    "path": {
                        "type": "string",
                        "description": "Path for disk usage (default: /)"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Limit for process listing (default: 10)"
                    },
                    "sort_by": {
                        "type": "string",
                        "enum": ["cpu", "memory"],
                        "description": "Sort processes by CPU or memory usage"
                    }
                },
                "required": ["operation"]
            }
        }
