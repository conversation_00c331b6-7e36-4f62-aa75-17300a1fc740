"""
Base tool interface for AI Terminal.

Defines the common interface that all tools must implement
for consistent integration with the agent system.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from ai_terminal.core.logger import LoggerMixin


class BaseTool(ABC, LoggerMixin):
    """
    Base class for all AI Terminal tools.
    
    Defines the interface that all tools must implement to ensure
    consistent behavior and integration with the agent system.
    """
    
    def __init__(self, name: str, description: str):
        """Initialize the tool."""
        self.name = name
        self.description = description
        self.enabled = True
        self.requires_approval = False
        self.timeout = 30  # Default timeout in seconds
    
    @abstractmethod
    async def execute(self, **kwargs) -> Any:
        """
        Execute the tool with the given arguments.
        
        Args:
            **kwargs: Tool-specific arguments
            
        Returns:
            Tool execution result
        """
        pass
    
    @abstractmethod
    def get_schema(self) -> Dict[str, Any]:
        """
        Get the JSON schema for this tool.
        
        Returns:
            JSON schema describing the tool's parameters
        """
        pass
    
    def validate_args(self, args: Dict[str, Any]) -> bool:
        """
        Validate tool arguments against the schema.
        
        Args:
            args: Arguments to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            schema = self.get_schema()
            required_params = schema.get("parameters", {}).get("required", [])
            
            # Check required parameters
            for param in required_params:
                if param not in args:
                    self.logger.error(f"Missing required parameter: {param}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating arguments: {e}")
            return False
    
    def is_dangerous(self, args: Dict[str, Any]) -> bool:
        """
        Check if the tool execution with given args is potentially dangerous.
        
        Args:
            args: Tool arguments
            
        Returns:
            True if potentially dangerous, False otherwise
        """
        return self.requires_approval
    
    async def pre_execute(self, args: Dict[str, Any]) -> bool:
        """
        Pre-execution hook for validation and setup.
        
        Args:
            args: Tool arguments
            
        Returns:
            True to continue execution, False to abort
        """
        if not self.enabled:
            self.logger.warning(f"Tool {self.name} is disabled")
            return False
        
        if not self.validate_args(args):
            self.logger.error(f"Invalid arguments for tool {self.name}")
            return False
        
        return True
    
    async def post_execute(self, args: Dict[str, Any], result: Any) -> Any:
        """
        Post-execution hook for cleanup and result processing.
        
        Args:
            args: Tool arguments
            result: Execution result
            
        Returns:
            Processed result
        """
        return result
    
    def get_help(self) -> str:
        """Get help text for this tool."""
        schema = self.get_schema()
        parameters = schema.get("parameters", {})
        properties = parameters.get("properties", {})
        required = parameters.get("required", [])
        
        help_text = f"**{self.name}**\n\n{self.description}\n\n"
        
        if properties:
            help_text += "**Parameters:**\n"
            for param_name, param_info in properties.items():
                param_type = param_info.get("type", "string")
                param_desc = param_info.get("description", "No description")
                required_marker = " (required)" if param_name in required else ""
                
                help_text += f"- `{param_name}` ({param_type}){required_marker}: {param_desc}\n"
        
        return help_text
    
    def __str__(self) -> str:
        """String representation of the tool."""
        return f"{self.name}: {self.description}"
    
    def __repr__(self) -> str:
        """Detailed string representation of the tool."""
        return f"<{self.__class__.__name__}(name='{self.name}', enabled={self.enabled})>"


class ToolError(Exception):
    """Base exception for tool errors."""
    
    def __init__(self, message: str, tool_name: str, error_code: Optional[str] = None):
        """Initialize tool error."""
        super().__init__(message)
        self.tool_name = tool_name
        self.error_code = error_code


class ToolExecutionError(ToolError):
    """Tool execution error."""
    pass


class ToolValidationError(ToolError):
    """Tool validation error."""
    pass


class ToolTimeoutError(ToolError):
    """Tool timeout error."""
    pass


class ToolPermissionError(ToolError):
    """Tool permission error."""
    pass
