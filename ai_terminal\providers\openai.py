"""
OpenAI provider implementation for AI Terminal.

Provides integration with OpenAI's GPT models including GPT-3.5 and GPT-4
with support for streaming, function calling, and vision capabilities.
"""

from typing import Any, Dict, List, Optional

import openai

from ai_terminal.providers.base import BaseProvider, ProviderError, AuthenticationError


class OpenAIProvider(BaseProvider):
    """OpenAI provider implementation."""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """Initialize OpenAI provider."""
        super().__init__(name, config)
        self.client: Optional[openai.AsyncOpenAI] = None
        self.default_model = config.get("model", "gpt-3.5-turbo")
    
    async def initialize(self) -> None:
        """Initialize the OpenAI provider."""
        try:
            if not self.api_key:
                raise AuthenticationError("OpenAI API key is required", self.name)
            
            self.client = openai.AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url
            )
            
            await self.validate_connection()
            self.is_initialized = True
            self.logger.info("OpenAI provider initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize OpenAI provider: {e}")
            raise
    
    async def generate_response(
        self,
        messages: List[Dict[str, Any]],
        model: Optional[str] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate response from OpenAI API."""
        if not self.client:
            raise ProviderError("Provider not initialized", self.name)
        
        model = model or self.default_model
        
        try:
            # Prepare request parameters
            params = {
                "model": model,
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", self.max_tokens),
                "temperature": kwargs.get("temperature", self.temperature),
                "stream": stream,
            }
            
            if tools:
                params["tools"] = self.format_tools(tools)
                params["tool_choice"] = "auto"
            
            # Make API call
            response = await self.client.chat.completions.create(**params)
            
            if stream:
                # Handle streaming response
                return {"stream": self._handle_streaming_response(response)}
            else:
                # Handle single response
                return self.parse_response(response)
                
        except Exception as e:
            self.logger.error(f"OpenAI API error: {e}")
            raise ProviderError(f"API request failed: {e}", self.name)
    
    async def _handle_streaming_response(self, response):
        """Handle streaming response from OpenAI."""
        async for chunk in response:
            yield self.parse_streaming_chunk(chunk)
    
    def parse_response(self, response) -> Dict[str, Any]:
        """Parse OpenAI API response."""
        choice = response.choices[0]
        message = choice.message
        
        return {
            "content": message.content or "",
            "tool_calls": [
                {
                    "id": tc.id,
                    "type": tc.type,
                    "function": {
                        "name": tc.function.name,
                        "arguments": tc.function.arguments
                    }
                }
                for tc in (message.tool_calls or [])
            ],
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens,
            },
            "model": response.model,
            "provider": self.name,
            "finish_reason": choice.finish_reason,
        }
    
    def parse_streaming_chunk(self, chunk) -> Dict[str, Any]:
        """Parse streaming response chunk."""
        choice = chunk.choices[0] if chunk.choices else None
        delta = choice.delta if choice else None
        
        return {
            "content": delta.content if delta and delta.content else "",
            "tool_calls": delta.tool_calls if delta and delta.tool_calls else [],
            "finish_reason": choice.finish_reason if choice else None,
            "model": chunk.model,
            "provider": self.name,
        }
    
    async def validate_connection(self) -> bool:
        """Validate connection to OpenAI API."""
        try:
            if not self.client:
                return False
            
            # Test with a simple request
            response = await self.client.chat.completions.create(
                model=self.default_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            return bool(response)
            
        except Exception as e:
            self.logger.error(f"OpenAI connection validation failed: {e}")
            return False
    
    async def list_models(self) -> List[str]:
        """List available OpenAI models."""
        try:
            if not self.client:
                return []
            
            models = await self.client.models.list()
            return [model.id for model in models.data if "gpt" in model.id]
            
        except Exception as e:
            self.logger.error(f"Failed to list OpenAI models: {e}")
            return ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo-preview"]
    
    async def get_model_info(self, model: str) -> Dict[str, Any]:
        """Get information about an OpenAI model."""
        model_info = {
            "gpt-3.5-turbo": {
                "name": "GPT-3.5 Turbo",
                "context_window": 4096,
                "supports_function_calling": True,
            },
            "gpt-4": {
                "name": "GPT-4",
                "context_window": 8192,
                "supports_function_calling": True,
            },
            "gpt-4-turbo-preview": {
                "name": "GPT-4 Turbo",
                "context_window": 128000,
                "supports_function_calling": True,
            },
        }
        
        return model_info.get(model, {"name": model, "context_window": 4096})
    
    def supports_vision(self) -> bool:
        """OpenAI supports vision in GPT-4V models."""
        return "vision" in self.model.lower() or "gpt-4v" in self.model.lower()
    
    async def shutdown(self) -> None:
        """Shutdown the OpenAI provider."""
        if self.client:
            await self.client.close()
            self.client = None
        await super().shutdown()
