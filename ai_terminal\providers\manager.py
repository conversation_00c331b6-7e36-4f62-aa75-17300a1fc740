"""
Provider manager for AI Terminal.

Manages multiple AI providers, handles provider selection, failover,
and provides a unified interface for AI interactions.
"""

import asyncio
from typing import Dict, List, Optional, Type

from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin
from ai_terminal.providers.base import Base<PERSON>rovider, ProviderError
from ai_terminal.providers.deepseek import DeepSeekProvider
from ai_terminal.providers.openai import OpenAIProvider
from ai_terminal.providers.anthropic import AnthropicProvider
from ai_terminal.providers.ollama import OllamaProvider
from ai_terminal.providers.gemini import GeminiProvider


class ProviderManager(LoggerMixin):
    """
    AI Provider Manager.
    
    Manages multiple AI providers, handles initialization, selection,
    and provides failover capabilities for robust AI interactions.
    """
    
    # Registry of available provider classes
    PROVIDER_CLASSES: Dict[str, Type[BaseProvider]] = {
        "deepseek": DeepSeekProvider,
        "openai": OpenAIProvider,
        "anthropic": AnthropicProvider,
        "ollama": <PERSON><PERSON><PERSON>Provider,
        "gemini": <PERSON><PERSON><PERSON><PERSON>,
    }
    
    def __init__(self, config: Config):
        """Initialize provider manager."""
        self.config = config
        self.providers: Dict[str, BaseProvider] = {}
        self.initialized_providers: Dict[str, bool] = {}
        self.provider_health: Dict[str, bool] = {}
        
        # Failover settings
        self.enable_failover = config.get("ai.enable_failover", True)
        self.failover_order = config.get("ai.failover_order", [])
    
    async def initialize(self) -> None:
        """Initialize all configured providers."""
        try:
            provider_configs = self.config.get("ai.providers", {})
            
            if not provider_configs:
                self.logger.warning("No AI providers configured")
                return
            
            # Initialize each configured provider
            for provider_name, provider_config in provider_configs.items():
                if not provider_config.get("enabled", True):
                    continue
                
                await self._initialize_provider(provider_name, provider_config)
            
            # Perform initial health checks
            await self._perform_health_checks()
            
            self.logger.info(f"Provider manager initialized with {len(self.providers)} providers")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize provider manager: {e}")
            raise
    
    async def _initialize_provider(self, name: str, config: Dict) -> None:
        """Initialize a single provider."""
        try:
            if name not in self.PROVIDER_CLASSES:
                self.logger.warning(f"Unknown provider type: {name}")
                return
            
            # Get API key from secure storage
            api_key = self.config.get_api_key(name)
            if not api_key and name != "ollama":  # Ollama doesn't need API key
                self.logger.warning(f"No API key found for provider: {name}")
                return
            
            # Create provider configuration
            provider_config = {
                **config,
                "api_key": api_key,
            }
            
            # Create and initialize provider
            provider_class = self.PROVIDER_CLASSES[name]
            provider = provider_class(name, provider_config)
            
            await provider.initialize()
            
            self.providers[name] = provider
            self.initialized_providers[name] = True
            
            self.logger.info(f"Initialized provider: {name}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize provider {name}: {e}")
            self.initialized_providers[name] = False
    
    async def get_provider(self, name: str) -> Optional[BaseProvider]:
        """Get a provider by name."""
        if name in self.providers and self.initialized_providers.get(name, False):
            return self.providers[name]
        return None
    
    async def list_providers(self) -> List[str]:
        """List all available provider names."""
        return [
            name for name, initialized in self.initialized_providers.items()
            if initialized
        ]
    
    async def get_healthy_providers(self) -> List[str]:
        """Get list of healthy provider names."""
        healthy = []
        for name, provider in self.providers.items():
            if self.provider_health.get(name, False):
                healthy.append(name)
        return healthy
    
    async def get_best_provider(self, preferred: Optional[str] = None) -> Optional[BaseProvider]:
        """
        Get the best available provider.
        
        Args:
            preferred: Preferred provider name
            
        Returns:
            Best available provider or None
        """
        # Try preferred provider first
        if preferred:
            provider = await self.get_provider(preferred)
            if provider and self.provider_health.get(preferred, False):
                return provider
        
        # Try default provider
        default_provider = self.config.get("ai.default_provider")
        if default_provider:
            provider = await self.get_provider(default_provider)
            if provider and self.provider_health.get(default_provider, False):
                return provider
        
        # Try failover order
        if self.enable_failover:
            for provider_name in self.failover_order:
                provider = await self.get_provider(provider_name)
                if provider and self.provider_health.get(provider_name, False):
                    self.logger.info(f"Using failover provider: {provider_name}")
                    return provider
        
        # Try any healthy provider
        healthy_providers = await self.get_healthy_providers()
        if healthy_providers:
            provider_name = healthy_providers[0]
            self.logger.info(f"Using fallback provider: {provider_name}")
            return await self.get_provider(provider_name)
        
        return None
    
    async def _perform_health_checks(self) -> None:
        """Perform health checks on all providers."""
        tasks = []
        for name, provider in self.providers.items():
            tasks.append(self._check_provider_health(name, provider))
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _check_provider_health(self, name: str, provider: BaseProvider) -> None:
        """Check health of a single provider."""
        try:
            is_healthy = await provider.validate_connection()
            self.provider_health[name] = is_healthy
            
            if is_healthy:
                self.logger.debug(f"Provider {name} is healthy")
            else:
                self.logger.warning(f"Provider {name} health check failed")
                
        except Exception as e:
            self.logger.error(f"Health check error for {name}: {e}")
            self.provider_health[name] = False
    
    async def add_provider(
        self, 
        name: str, 
        provider_type: str, 
        config: Dict
    ) -> bool:
        """Add a new provider at runtime."""
        try:
            if provider_type not in self.PROVIDER_CLASSES:
                raise ValueError(f"Unknown provider type: {provider_type}")
            
            # Initialize the provider
            await self._initialize_provider(name, config)
            
            # Update configuration
            self.config.set(f"ai.providers.{name}", config)
            
            # Perform health check
            if name in self.providers:
                await self._check_provider_health(name, self.providers[name])
            
            return name in self.providers and self.initialized_providers.get(name, False)
            
        except Exception as e:
            self.logger.error(f"Failed to add provider {name}: {e}")
            return False
    
    async def remove_provider(self, name: str) -> bool:
        """Remove a provider."""
        try:
            if name in self.providers:
                await self.providers[name].shutdown()
                del self.providers[name]
            
            if name in self.initialized_providers:
                del self.initialized_providers[name]
            
            if name in self.provider_health:
                del self.provider_health[name]
            
            self.logger.info(f"Removed provider: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to remove provider {name}: {e}")
            return False
    
    async def test_provider(self, name: str) -> Dict:
        """Test a provider and return detailed results."""
        try:
            provider = await self.get_provider(name)
            if not provider:
                return {
                    "provider": name,
                    "success": False,
                    "error": "Provider not found or not initialized"
                }
            
            # Perform health check
            health_result = await provider.health_check()
            
            # Test basic functionality
            test_messages = [
                {"role": "user", "content": "Hello, this is a test message."}
            ]
            
            try:
                response = await provider.generate_response(test_messages)
                test_success = bool(response.get("content"))
            except Exception as e:
                test_success = False
                health_result["test_error"] = str(e)
            
            health_result["test_success"] = test_success
            return health_result
            
        except Exception as e:
            return {
                "provider": name,
                "success": False,
                "error": str(e)
            }
    
    async def get_provider_stats(self) -> Dict:
        """Get statistics for all providers."""
        stats = {
            "total_providers": len(self.PROVIDER_CLASSES),
            "configured_providers": len(self.providers),
            "healthy_providers": len(await self.get_healthy_providers()),
            "providers": {}
        }
        
        for name, provider in self.providers.items():
            health_info = await provider.health_check()
            stats["providers"][name] = {
                "initialized": self.initialized_providers.get(name, False),
                "healthy": self.provider_health.get(name, False),
                "model": provider.model,
                "supports_streaming": provider.supports_streaming(),
                "supports_function_calling": provider.supports_function_calling(),
                "context_window": provider.get_context_window(),
                **health_info
            }
        
        return stats
    
    async def refresh_health_checks(self) -> None:
        """Refresh health checks for all providers."""
        await self._perform_health_checks()
        self.logger.info("Provider health checks refreshed")
    
    async def shutdown(self) -> None:
        """Shutdown all providers."""
        try:
            tasks = []
            for provider in self.providers.values():
                tasks.append(provider.shutdown())
            
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
            
            self.providers.clear()
            self.initialized_providers.clear()
            self.provider_health.clear()
            
            self.logger.info("All providers shutdown")
            
        except Exception as e:
            self.logger.error(f"Error during provider shutdown: {e}")
