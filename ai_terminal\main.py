#!/usr/bin/env python3
"""
Main entry point for the AI Terminal application.

This module provides the command-line interface and application initialization.
"""

import asyncio
import sys
import traceback
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.traceback import install

from ai_terminal.core.app import AITerminalApp
from ai_terminal.core.config import Config
from ai_terminal.core.logger import setup_logging
from ai_terminal.ui.onboarding import OnboardingWizard

# Install rich traceback handler for better error display
install(show_locals=True)

console = Console()


@click.command()
@click.option(
    "--config-dir",
    type=click.Path(exists=False, file_okay=False, dir_okay=True, path_type=Path),
    help="Custom configuration directory path",
)
@click.option(
    "--log-level",
    type=click.Choice(["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]),
    default="INFO",
    help="Set the logging level",
)
@click.option(
    "--no-onboarding",
    is_flag=True,
    help="Skip onboarding wizard even if not configured",
)
@click.option(
    "--model",
    type=str,
    help="Override default AI model",
)
@click.option(
    "--provider",
    type=str,
    help="Override default AI provider",
)
@click.option(
    "--session",
    type=str,
    help="Load specific session by name",
)
@click.option(
    "--debug",
    is_flag=True,
    help="Enable debug mode with verbose logging",
)
@click.version_option(version="1.0.0", prog_name="AI Terminal")
def main(
    config_dir: Optional[Path] = None,
    log_level: str = "INFO",
    no_onboarding: bool = False,
    model: Optional[str] = None,
    provider: Optional[str] = None,
    session: Optional[str] = None,
    debug: bool = False,
) -> None:
    """
    AI Terminal - Sophisticated autonomous AI-powered CLI terminal application.
    
    A production-ready terminal interface for interacting with AI models,
    featuring autonomous agents, comprehensive tool integration, and
    enterprise-grade security and reliability.
    """
    try:
        # Setup logging
        if debug:
            log_level = "DEBUG"
        
        setup_logging(log_level)
        
        # Initialize configuration
        config = Config(config_dir=config_dir)
        
        # Override configuration with CLI arguments
        if model:
            config.set("ai.default_model", model)
        if provider:
            config.set("ai.default_provider", provider)
        if session:
            config.set("session.default_session", session)
        
        # Run onboarding if needed
        if not no_onboarding and not config.is_configured():
            console.print(
                "[bold blue]Welcome to AI Terminal![/bold blue]\n"
                "Let's get you set up with your first AI provider.\n"
            )
            
            wizard = OnboardingWizard(config)
            if not asyncio.run(wizard.run()):
                console.print("[yellow]Setup cancelled. Exiting...[/yellow]")
                sys.exit(1)
        
        # Check if configuration is valid
        if not config.is_configured():
            console.print(
                "[bold red]Error:[/bold red] AI Terminal is not configured.\n"
                "Please run the setup wizard or configure manually.\n"
                "Use --help for more information."
            )
            sys.exit(1)
        
        # Initialize and run the main application
        app = AITerminalApp(config)
        asyncio.run(app.run())
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted by user. Goodbye![/yellow]")
        sys.exit(0)
    except Exception as e:
        console.print(f"[bold red]Fatal error:[/bold red] {e}")
        if debug:
            console.print_exception()
        else:
            console.print(
                "\n[dim]Run with --debug for detailed error information.[/dim]"
            )
        sys.exit(1)


def cli() -> None:
    """Entry point for the CLI command."""
    main()


if __name__ == "__main__":
    main()
