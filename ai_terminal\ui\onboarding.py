"""
Onboarding wizard for first-time AI Terminal setup.

Provides an interactive setup experience for configuring AI providers,
API keys, and initial preferences.
"""

import asyncio
from typing import Dict, List, Optional

from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm, Prompt
from rich.table import Table
from rich.text import Text

from ai_terminal.core.config import AIProviderConfig, Config
from ai_terminal.core.logger import LoggerMixin


class OnboardingWizard(LoggerMixin):
    """Interactive onboarding wizard for first-time setup."""
    
    def __init__(self, config: Config):
        """Initialize the onboarding wizard."""
        self.config = config
        self.console = Console()
        
        # Available AI providers with their configurations
        self.available_providers = {
            "deepseek": {
                "name": "DeepSeek",
                "description": "High-performance AI models with competitive pricing",
                "models": ["deepseek-chat", "deepseek-coder"],
                "api_url": "https://api.deepseek.com",
                "setup_url": "https://platform.deepseek.com/api_keys",
            },
            "openai": {
                "name": "OpenAI",
                "description": "GPT-3.5, GPT-4, and other OpenAI models",
                "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo-preview"],
                "api_url": "https://api.openai.com/v1",
                "setup_url": "https://platform.openai.com/api-keys",
            },
            "anthropic": {
                "name": "Anthropic",
                "description": "Claude models for advanced reasoning",
                "models": ["claude-3-sonnet", "claude-3-opus", "claude-3-haiku"],
                "api_url": "https://api.anthropic.com",
                "setup_url": "https://console.anthropic.com/",
            },
            "ollama": {
                "name": "Ollama",
                "description": "Local AI models running on your machine",
                "models": ["llama2", "codellama", "mistral", "neural-chat"],
                "api_url": "http://localhost:11434",
                "setup_url": "https://ollama.ai/download",
            },
            "gemini": {
                "name": "Google Gemini",
                "description": "Google's advanced AI models",
                "models": ["gemini-pro", "gemini-pro-vision"],
                "api_url": "https://generativelanguage.googleapis.com",
                "setup_url": "https://makersuite.google.com/app/apikey",
            },
        }
    
    async def run(self) -> bool:
        """Run the onboarding wizard."""
        try:
            self.console.print(Panel.fit(
                "[bold blue]AI Terminal Setup Wizard[/bold blue]\n\n"
                "Welcome! Let's configure your AI Terminal with your preferred AI provider.\n"
                "This wizard will help you set up API keys and choose your default model.",
                title="🤖 Setup",
                border_style="blue"
            ))
            
            # Show available providers
            self._show_available_providers()
            
            # Select provider
            provider_key = self._select_provider()
            if not provider_key:
                return False
            
            # Configure the selected provider
            if not await self._configure_provider(provider_key):
                return False
            
            # Configure basic preferences
            self._configure_preferences()
            
            # Show completion message
            self._show_completion()
            
            return True
            
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Setup cancelled by user.[/yellow]")
            return False
        except Exception as e:
            self.logger.error(f"Onboarding error: {e}")
            self.console.print(f"\n[red]Setup failed: {e}[/red]")
            return False
    
    def _show_available_providers(self) -> None:
        """Display available AI providers."""
        table = Table(title="Available AI Providers", show_header=True, header_style="bold magenta")
        table.add_column("Key", style="cyan", no_wrap=True)
        table.add_column("Provider", style="green")
        table.add_column("Description", style="white")
        table.add_column("Models", style="yellow")
        
        for key, provider in self.available_providers.items():
            models_str = ", ".join(provider["models"][:3])
            if len(provider["models"]) > 3:
                models_str += f" (+{len(provider['models']) - 3} more)"
            
            table.add_row(
                key,
                provider["name"],
                provider["description"],
                models_str
            )
        
        self.console.print(table)
        self.console.print()
    
    def _select_provider(self) -> Optional[str]:
        """Let user select an AI provider."""
        while True:
            provider_key = Prompt.ask(
                "Which AI provider would you like to configure?",
                choices=list(self.available_providers.keys()),
                default="deepseek"
            )
            
            if provider_key in self.available_providers:
                provider = self.available_providers[provider_key]
                
                self.console.print(f"\n[green]Selected: {provider['name']}[/green]")
                self.console.print(f"Description: {provider['description']}")
                self.console.print(f"Setup URL: {provider['setup_url']}")
                
                if Confirm.ask("\nIs this correct?", default=True):
                    return provider_key
            else:
                self.console.print("[red]Invalid selection. Please try again.[/red]")
    
    async def _configure_provider(self, provider_key: str) -> bool:
        """Configure the selected AI provider."""
        provider_info = self.available_providers[provider_key]
        
        self.console.print(f"\n[bold]Configuring {provider_info['name']}[/bold]")
        
        # Special handling for Ollama (local)
        if provider_key == "ollama":
            return await self._configure_ollama()
        
        # Get API key
        self.console.print(f"\nTo use {provider_info['name']}, you need an API key.")
        self.console.print(f"Get your API key from: {provider_info['setup_url']}")
        
        api_key = Prompt.ask(
            f"\nEnter your {provider_info['name']} API key",
            password=True
        )
        
        if not api_key:
            self.console.print("[red]API key is required.[/red]")
            return False
        
        # Select model
        model = self._select_model(provider_info["models"])
        if not model:
            return False
        
        # Create provider configuration
        provider_config = AIProviderConfig(
            name=provider_key,
            api_key_service=f"ai-terminal-{provider_key}",
            api_key_username="api-key",
            base_url=provider_info["api_url"],
            model=model,
            enabled=True
        )
        
        # Save configuration
        self.config.add_provider(provider_config)
        self.config.set_api_key(provider_key, api_key)
        self.config.set("ai.default_provider", provider_key)
        self.config.set("ai.default_model", model)
        
        self.console.print(f"[green]✓ {provider_info['name']} configured successfully![/green]")
        return True
    
    async def _configure_ollama(self) -> bool:
        """Configure Ollama local AI provider."""
        self.console.print("\n[yellow]Ollama runs AI models locally on your machine.[/yellow]")
        self.console.print("Make sure Ollama is installed and running.")
        self.console.print("Download from: https://ollama.ai/download")
        
        if not Confirm.ask("\nIs Ollama installed and running?", default=False):
            self.console.print("[yellow]Please install and start Ollama first.[/yellow]")
            return False
        
        # Test connection
        try:
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:11434/api/tags")
                if response.status_code == 200:
                    models_data = response.json()
                    available_models = [model["name"] for model in models_data.get("models", [])]
                    
                    if not available_models:
                        self.console.print("[yellow]No models found. Please pull a model first.[/yellow]")
                        self.console.print("Example: ollama pull llama2")
                        return False
                    
                    model = self._select_model(available_models)
                    if not model:
                        return False
                    
                else:
                    self.console.print("[red]Could not connect to Ollama.[/red]")
                    return False
                    
        except Exception as e:
            self.console.print(f"[red]Error connecting to Ollama: {e}[/red]")
            return False
        
        # Create Ollama configuration
        provider_config = AIProviderConfig(
            name="ollama",
            api_key_service="",  # No API key needed for local
            api_key_username="",
            base_url="http://localhost:11434",
            model=model,
            enabled=True
        )
        
        self.config.add_provider(provider_config)
        self.config.set("ai.default_provider", "ollama")
        self.config.set("ai.default_model", model)
        
        self.console.print("[green]✓ Ollama configured successfully![/green]")
        return True
    
    def _select_model(self, models: List[str]) -> Optional[str]:
        """Let user select a model."""
        if len(models) == 1:
            return models[0]
        
        self.console.print("\nAvailable models:")
        for i, model in enumerate(models, 1):
            self.console.print(f"  {i}. {model}")
        
        while True:
            try:
                choice = Prompt.ask(
                    f"\nSelect a model (1-{len(models)})",
                    default="1"
                )
                index = int(choice) - 1
                if 0 <= index < len(models):
                    return models[index]
                else:
                    self.console.print("[red]Invalid selection.[/red]")
            except ValueError:
                self.console.print("[red]Please enter a number.[/red]")
    
    def _configure_preferences(self) -> None:
        """Configure basic user preferences."""
        self.console.print("\n[bold]Basic Preferences[/bold]")
        
        # Theme selection
        theme = Prompt.ask(
            "Choose UI theme",
            choices=["dark", "light"],
            default="dark"
        )
        self.config.set("ui.theme", theme)
        
        # Safety settings
        require_approval = Confirm.ask(
            "Require approval for potentially dangerous commands?",
            default=True
        )
        self.config.set("security.require_approval", require_approval)
        
        # Auto-save sessions
        auto_save = Confirm.ask(
            "Automatically save conversation sessions?",
            default=True
        )
        self.config.set("session.auto_save", auto_save)
    
    def _show_completion(self) -> None:
        """Show setup completion message."""
        self.console.print(Panel.fit(
            "[bold green]Setup Complete! 🎉[/bold green]\n\n"
            "Your AI Terminal is now configured and ready to use.\n\n"
            "Quick tips:\n"
            "• Type your questions or commands naturally\n"
            "• Use /help to see available slash commands\n"
            "• Use /model to switch between AI models\n"
            "• Use /config to modify settings\n\n"
            "Enjoy your AI-powered terminal experience!",
            title="✅ Ready",
            border_style="green"
        ))
