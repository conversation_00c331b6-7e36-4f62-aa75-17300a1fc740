"""
Security validator for AI Terminal.

Provides comprehensive security validation for commands, file operations,
and user inputs with configurable safety policies.
"""

import re
from pathlib import Path
from typing import Any, Dict, List, Optional

from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin


class SecurityValidator(LoggerMixin):
    """
    Security validator for AI Terminal operations.
    
    Validates commands, file operations, and user inputs against
    security policies and dangerous operation patterns.
    """
    
    def __init__(self, config: Config):
        """Initialize security validator."""
        self.config = config
        self.security_config = config.get_security_config()
        
        # Dangerous command patterns
        self.dangerous_patterns = [
            r'\brm\s+-rf\s+/',
            r'\bformat\s+[a-zA-Z]:',
            r'\bdd\s+if=.*of=',
            r'\bmkfs\.',
            r'\bfdisk\s+',
            r'\bshutdown\s+',
            r'\breboot\s+',
            r'\bhalt\s+',
            r'\bpoweroff\s+',
            r'>\s*/dev/',
            r'>\s*/proc/',
            r'>\s*/sys/',
        ]
        
        # Network operation patterns
        self.network_patterns = [
            r'\bcurl\s+',
            r'\bwget\s+',
            r'\bnc\s+',
            r'\bnetcat\s+',
            r'\btelnet\s+',
            r'\bssh\s+',
            r'\bftp\s+',
            r'\brsync\s+.*@',
        ]
        
        # Code execution patterns
        self.execution_patterns = [
            r'\beval\s*\(',
            r'\bexec\s*\(',
            r'python\s+-c\s+',
            r'perl\s+-e\s+',
            r'ruby\s+-e\s+',
            r'node\s+-e\s+',
            r'bash\s+-c\s+',
            r'sh\s+-c\s+',
        ]
        
        # Restricted file paths
        self.restricted_paths = [
            '/etc/',
            '/bin/',
            '/sbin/',
            '/usr/bin/',
            '/usr/sbin/',
            '/boot/',
            '/dev/',
            '/proc/',
            '/sys/',
            'C:\\Windows\\',
            'C:\\Program Files\\',
            'C:\\System32\\',
        ]
    
    def validate_command(self, command: str) -> Dict[str, Any]:
        """
        Validate a shell command for security risks.
        
        Args:
            command: Shell command to validate
            
        Returns:
            Validation result with risk assessment
        """
        result = {
            "safe": True,
            "risk_level": "low",
            "warnings": [],
            "blocked_reasons": [],
            "requires_approval": False,
        }
        
        if not command or not command.strip():
            result["blocked_reasons"].append("Empty command")
            result["safe"] = False
            return result
        
        command_lower = command.lower().strip()
        
        # Check dangerous patterns
        for pattern in self.dangerous_patterns:
            if re.search(pattern, command_lower):
                result["warnings"].append(f"Dangerous pattern detected: {pattern}")
                result["risk_level"] = "high"
                result["requires_approval"] = True
        
        # Check network operations
        for pattern in self.network_patterns:
            if re.search(pattern, command_lower):
                result["warnings"].append("Network operation detected")
                result["risk_level"] = "medium"
                result["requires_approval"] = True
        
        # Check code execution
        for pattern in self.execution_patterns:
            if re.search(pattern, command_lower):
                result["warnings"].append("Code execution detected")
                result["risk_level"] = "high"
                result["requires_approval"] = True
        
        # Check against configured dangerous commands
        for dangerous_cmd in self.security_config.dangerous_commands:
            if dangerous_cmd.lower() in command_lower:
                result["warnings"].append(f"Dangerous command: {dangerous_cmd}")
                result["risk_level"] = "high"
                result["requires_approval"] = True
        
        # Check command length
        if len(command) > 1000:
            result["warnings"].append("Command is very long")
            result["risk_level"] = "medium"
        
        # Check for command injection patterns
        injection_patterns = [";", "&&", "||", "|", "`", "$(", "${"]
        for pattern in injection_patterns:
            if pattern in command:
                result["warnings"].append(f"Potential injection pattern: {pattern}")
                if result["risk_level"] == "low":
                    result["risk_level"] = "medium"
        
        # Final safety determination
        if result["risk_level"] == "high" or result["requires_approval"]:
            if self.security_config.require_approval:
                result["safe"] = False
        
        return result
    
    def validate_file_path(self, path: str, operation: str) -> Dict[str, Any]:
        """
        Validate a file path for security risks.
        
        Args:
            path: File path to validate
            operation: Type of operation (read, write, delete, etc.)
            
        Returns:
            Validation result
        """
        result = {
            "safe": True,
            "warnings": [],
            "blocked_reasons": [],
            "requires_approval": False,
        }
        
        try:
            file_path = Path(path).resolve()
        except Exception as e:
            result["blocked_reasons"].append(f"Invalid path: {e}")
            result["safe"] = False
            return result
        
        path_str = str(file_path)
        
        # Check for path traversal
        if ".." in path or path.startswith("/"):
            # Allow absolute paths in certain directories
            allowed = False
            safe_absolute_paths = [
                "/home/", "/tmp/", "/var/tmp/",
                "C:\\Users\\<USER>\\temp\\", "C:\\tmp\\"
            ]
            
            for safe_path in safe_absolute_paths:
                if path_str.startswith(safe_path):
                    allowed = True
                    break
            
            if not allowed:
                result["warnings"].append("Absolute path outside safe directories")
                result["requires_approval"] = True
        
        # Check restricted paths
        for restricted in self.restricted_paths:
            if path_str.startswith(restricted):
                result["blocked_reasons"].append(f"Access to restricted path: {restricted}")
                result["safe"] = False
                return result
        
        # Check file extension for write operations
        if operation in ("write", "create", "delete"):
            file_extension = file_path.suffix.lower()
            allowed_extensions = self.security_config.allowed_file_extensions
            
            if allowed_extensions and file_extension not in allowed_extensions:
                result["warnings"].append(f"File extension not in allowed list: {file_extension}")
                result["requires_approval"] = True
        
        # Special checks for delete operations
        if operation == "delete":
            result["requires_approval"] = True
            result["warnings"].append("File deletion requires approval")
        
        return result
    
    def validate_network_request(self, url: str, method: str = "GET") -> Dict[str, Any]:
        """
        Validate a network request for security risks.
        
        Args:
            url: URL to validate
            method: HTTP method
            
        Returns:
            Validation result
        """
        result = {
            "safe": True,
            "warnings": [],
            "blocked_reasons": [],
            "requires_approval": False,
        }
        
        # Check for localhost/internal IPs
        internal_patterns = [
            r'localhost',
            r'127\.0\.0\.1',
            r'192\.168\.',
            r'10\.',
            r'172\.(1[6-9]|2[0-9]|3[01])\.',
        ]
        
        for pattern in internal_patterns:
            if re.search(pattern, url.lower()):
                result["warnings"].append("Request to internal/localhost address")
                result["requires_approval"] = True
                break
        
        # Check for dangerous protocols
        if url.startswith(('file://', 'ftp://', 'sftp://')):
            result["warnings"].append("Potentially dangerous protocol")
            result["requires_approval"] = True
        
        # Check for non-standard ports
        port_match = re.search(r':(\d+)/', url)
        if port_match:
            port = int(port_match.group(1))
            if port not in [80, 443, 8080, 8443]:
                result["warnings"].append(f"Non-standard port: {port}")
                result["requires_approval"] = True
        
        return result
    
    def sanitize_input(self, user_input: str) -> str:
        """
        Sanitize user input to prevent injection attacks.
        
        Args:
            user_input: Raw user input
            
        Returns:
            Sanitized input
        """
        if not user_input:
            return ""
        
        # Remove null bytes
        sanitized = user_input.replace('\x00', '')
        
        # Limit length
        max_length = 10000
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length]
            self.logger.warning(f"Input truncated to {max_length} characters")
        
        return sanitized
    
    def check_approval_required(self, operation: str, context: Dict[str, Any]) -> bool:
        """
        Check if an operation requires user approval.
        
        Args:
            operation: Type of operation
            context: Operation context and parameters
            
        Returns:
            True if approval is required
        """
        if not self.security_config.require_approval:
            return False
        
        # Always require approval for these operations
        high_risk_operations = [
            "shell_execute",
            "file_delete",
            "directory_delete",
            "package_install",
            "package_uninstall",
            "system_shutdown",
            "network_request",
        ]
        
        if operation in high_risk_operations:
            return True
        
        # Check operation-specific contexts
        if operation == "shell" and "command" in context:
            validation = self.validate_command(context["command"])
            return validation["requires_approval"]
        
        if operation == "file_ops" and "path" in context:
            validation = self.validate_file_path(
                context["path"], 
                context.get("operation", "read")
            )
            return validation["requires_approval"]
        
        return False
    
    def get_security_summary(self) -> Dict[str, Any]:
        """Get current security configuration summary."""
        return {
            "require_approval": self.security_config.require_approval,
            "encrypt_storage": self.security_config.encrypt_storage,
            "dangerous_commands_count": len(self.security_config.dangerous_commands),
            "allowed_extensions_count": len(self.security_config.allowed_file_extensions),
            "validation_patterns": {
                "dangerous": len(self.dangerous_patterns),
                "network": len(self.network_patterns),
                "execution": len(self.execution_patterns),
            },
            "restricted_paths_count": len(self.restricted_paths),
        }
