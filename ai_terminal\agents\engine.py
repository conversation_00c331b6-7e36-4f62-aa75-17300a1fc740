"""
Autonomous Agent Engine for AI Terminal.

Core engine that orchestrates AI interactions, tool usage, conversation management,
and autonomous decision-making with advanced context management.
"""

import asyncio
import json
import time
from typing import Any, Callable, Dict, List, Optional

from ai_terminal.agents.response import AgentR<PERSON>ponse, StreamingResponse, ToolCall
from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin
from ai_terminal.providers.manager import ProviderManager
from ai_terminal.storage.models import Message
from ai_terminal.storage.session import SessionManager
from ai_terminal.tools.manager import ToolManager


class AgentEngine(LoggerMixin):
    """
    Autonomous AI Agent Engine.
    
    Orchestrates AI interactions with:
    - Multi-provider LLM support
    - Comprehensive tool integration
    - Context-aware conversation management
    - Autonomous decision-making
    - Streaming response handling
    - Security and approval workflows
    """
    
    def __init__(
        self,
        config: Config,
        provider_manager: ProviderManager,
        tool_manager: ToolManager,
        session_manager: SessionManager,
        security_validator=None,
        approval_manager=None,
    ):
        """Initialize the agent engine."""
        self.config = config
        self.provider_manager = provider_manager
        self.tool_manager = tool_manager
        self.session_manager = session_manager
        self.security_validator = security_validator
        self.approval_manager = approval_manager
        
        # Current state
        self.current_provider = config.get("ai.default_provider", "deepseek")
        self.current_model = config.get("ai.default_model", "deepseek-chat")
        self.is_processing = False
        
        # Context management
        self.max_context_tokens = 8000  # Conservative default
        self.context_window_usage = 0.8  # Use 80% of context window
        
        # Callbacks for UI integration
        self.on_thinking_start: Optional[Callable] = None
        self.on_thinking_stop: Optional[Callable] = None
        self.on_tool_call: Optional[Callable] = None
        self.on_approval_required: Optional[Callable] = None
        self.on_token_received: Optional[Callable] = None
        
        # System prompt for autonomous behavior
        self.system_prompt = self._build_system_prompt()
        
        self.logger.info("Agent engine initialized")
    
    async def initialize(self) -> None:
        """Initialize the agent engine."""
        try:
            # Validate current provider and model
            provider = await self.provider_manager.get_provider(self.current_provider)
            if not provider:
                # Fallback to first available provider
                available_providers = await self.provider_manager.list_providers()
                if available_providers:
                    self.current_provider = available_providers[0]
                    provider_config = self.config.get_provider_config(self.current_provider)
                    if provider_config:
                        self.current_model = provider_config.model
                else:
                    raise RuntimeError("No AI providers configured")
            
            # Update context window size based on model
            await self._update_context_window()
            
            self.logger.info(f"Agent engine ready with {self.current_provider}/{self.current_model}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize agent engine: {e}")
            raise
    
    async def process_message(self, user_input: str) -> Optional[AgentResponse]:
        """
        Process a user message and generate an AI response.
        
        Args:
            user_input: The user's input message
            
        Returns:
            AgentResponse with the AI's response and any tool calls
        """
        if self.is_processing:
            self.logger.warning("Agent is already processing a message")
            return None
        
        try:
            self.is_processing = True
            
            # Notify thinking start
            if self.on_thinking_start:
                await self.on_thinking_start()
            
            # Build conversation context
            messages = await self._build_conversation_context(user_input)
            
            # Get AI provider
            provider = await self.provider_manager.get_provider(self.current_provider)
            if not provider:
                raise RuntimeError(f"Provider {self.current_provider} not available")
            
            # Generate response with tool support
            response = await self._generate_response_with_tools(provider, messages)
            
            # Save messages to session
            await self._save_conversation(user_input, response)
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing message: {e}")
            return AgentResponse(
                content=f"I encountered an error: {e}",
                model=self.current_model,
                provider=self.current_provider,
            )
        finally:
            self.is_processing = False
            
            # Notify thinking stop
            if self.on_thinking_stop:
                await self.on_thinking_stop()
    
    async def _build_conversation_context(self, user_input: str) -> List[Dict[str, Any]]:
        """Build conversation context with proper token management."""
        messages = []
        
        # Add system prompt
        messages.append({
            "role": "system",
            "content": self.system_prompt
        })
        
        # Get recent conversation history
        current_session = await self.session_manager.get_current_session()
        if current_session:
            history = await self.session_manager.get_session_messages(
                current_session.id, 
                limit=50  # Get last 50 messages
            )
            
            # Convert to provider format and manage context window
            for msg in history[-20:]:  # Use last 20 messages to stay within context
                messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
        
        # Add current user input
        messages.append({
            "role": "user",
            "content": user_input
        })
        
        # Trim context if needed
        messages = await self._trim_context(messages)
        
        return messages
    
    async def _trim_context(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Trim conversation context to fit within token limits."""
        # Simple token estimation (4 chars ≈ 1 token)
        total_chars = sum(len(msg["content"]) for msg in messages)
        estimated_tokens = total_chars // 4
        
        max_tokens = int(self.max_context_tokens * self.context_window_usage)
        
        if estimated_tokens <= max_tokens:
            return messages
        
        # Keep system prompt and recent messages
        system_msg = messages[0] if messages and messages[0]["role"] == "system" else None
        user_msg = messages[-1] if messages and messages[-1]["role"] == "user" else None
        
        # Trim middle messages
        trimmed = []
        if system_msg:
            trimmed.append(system_msg)
        
        # Add recent messages that fit
        recent_messages = messages[1:-1] if len(messages) > 2 else []
        for msg in reversed(recent_messages):
            test_messages = [system_msg] + [msg] + trimmed[1:] + ([user_msg] if user_msg else [])
            test_chars = sum(len(m["content"]) for m in test_messages if m)
            if test_chars // 4 <= max_tokens:
                trimmed.insert(1, msg)
            else:
                break
        
        if user_msg:
            trimmed.append(user_msg)
        
        return trimmed
    
    async def _generate_response_with_tools(
        self, 
        provider, 
        messages: List[Dict[str, Any]]
    ) -> AgentResponse:
        """Generate response with tool calling support."""
        # Get available tools
        available_tools = await self.tool_manager.get_available_tools()
        tool_schemas = [tool.get_schema() for tool in available_tools]
        
        # Generate initial response
        response = await provider.generate_response(
            messages=messages,
            model=self.current_model,
            tools=tool_schemas,
            stream=True
        )
        
        # Handle streaming response
        if hasattr(response, 'stream'):
            streaming_response = StreamingResponse(self.current_model, self.current_provider)
            
            # Set up callbacks
            if self.on_token_received:
                streaming_response.set_token_callback(self.on_token_received)
            
            # Process stream
            async for chunk in response.stream():
                if chunk.get('content'):
                    streaming_response.add_token(chunk['content'])
                
                if chunk.get('tool_calls'):
                    for tool_call_data in chunk['tool_calls']:
                        tool_call = await self._execute_tool_call(tool_call_data)
                        streaming_response.add_tool_call(tool_call)
            
            return streaming_response.finish()
        
        # Handle non-streaming response
        agent_response = AgentResponse(
            content=response.get('content', ''),
            model=self.current_model,
            provider=self.current_provider,
            prompt_tokens=response.get('usage', {}).get('prompt_tokens', 0),
            completion_tokens=response.get('usage', {}).get('completion_tokens', 0),
            total_tokens=response.get('usage', {}).get('total_tokens', 0),
        )
        
        # Execute tool calls if present
        if response.get('tool_calls'):
            for tool_call_data in response['tool_calls']:
                tool_call = await self._execute_tool_call(tool_call_data)
                agent_response.add_tool_call(tool_call)
        
        return agent_response
    
    async def _execute_tool_call(self, tool_call_data: Dict[str, Any]) -> ToolCall:
        """Execute a tool call with security checks."""
        tool_name = tool_call_data.get('function', {}).get('name', '')
        tool_args = tool_call_data.get('function', {}).get('arguments', {})
        tool_id = tool_call_data.get('id', f"call_{int(time.time())}")
        
        # Parse arguments if they're a string
        if isinstance(tool_args, str):
            try:
                tool_args = json.loads(tool_args)
            except json.JSONDecodeError:
                return ToolCall(
                    id=tool_id,
                    name=tool_name,
                    args=tool_args,
                    error="Invalid JSON in tool arguments"
                )
        
        tool_call = ToolCall(
            id=tool_id,
            name=tool_name,
            args=tool_args
        )
        
        try:
            # Notify UI of tool call
            if self.on_tool_call:
                await self.on_tool_call(tool_name, tool_args)
            
            # Check if approval is required
            if await self._requires_approval(tool_name, tool_args):
                if self.approval_manager:
                    approved = await self.approval_manager.request_approval(
                        operation=tool_name,
                        description=f"Execute {tool_name} with provided arguments",
                        context=tool_args,
                        risk_level="medium"
                    )
                    if not approved:
                        tool_call.error = "User denied approval"
                        return tool_call
                elif self.on_approval_required:
                    approved = await self.on_approval_required(
                        f"{tool_name}({tool_args})",
                        f"Execute {tool_name} with provided arguments"
                    )
                    if not approved:
                        tool_call.error = "User denied approval"
                        return tool_call
            
            # Execute the tool
            start_time = time.time()
            result = await self.tool_manager.execute_tool(tool_name, tool_args)
            execution_time = time.time() - start_time
            
            tool_call.result = result
            tool_call.execution_time = execution_time
            
        except Exception as e:
            self.logger.error(f"Tool execution error: {e}")
            tool_call.error = str(e)
        
        return tool_call
    
    async def _requires_approval(self, tool_name: str, tool_args: Dict[str, Any]) -> bool:
        """Check if a tool call requires user approval."""
        if self.security_validator:
            return self.security_validator.check_approval_required(tool_name, tool_args)

        # Fallback to basic security check
        security_config = self.config.get_security_config()

        if not security_config.require_approval:
            return False

        # Check for dangerous commands
        dangerous_commands = security_config.dangerous_commands

        # Check tool name
        if tool_name in ["shell_execute", "file_delete", "system_shutdown"]:
            return True

        # Check command content for shell tools
        if tool_name == "shell" and "command" in tool_args:
            command = tool_args["command"].lower()
            return any(dangerous_cmd in command for dangerous_cmd in dangerous_commands)

        return False
    
    async def _save_conversation(self, user_input: str, response: AgentResponse) -> None:
        """Save conversation to current session."""
        try:
            current_session = await self.session_manager.get_current_session()
            if not current_session:
                # Create default session
                current_session = await self.session_manager.create_session("main")
            
            # Save user message
            await self.session_manager.add_message(
                session_id=current_session.id,
                role="user",
                content=user_input
            )
            
            # Save assistant response
            await self.session_manager.add_message(
                session_id=current_session.id,
                role="assistant",
                content=response.content,
                model=response.model,
                tool_calls=[tc.to_dict() for tc in response.tool_calls] if response.tool_calls else None
            )
            
        except Exception as e:
            self.logger.error(f"Failed to save conversation: {e}")
    
    async def switch_model(self, provider: str, model: str) -> None:
        """Switch to a different AI model."""
        try:
            # Validate provider exists
            provider_instance = await self.provider_manager.get_provider(provider)
            if not provider_instance:
                raise ValueError(f"Provider {provider} not available")
            
            self.current_provider = provider
            self.current_model = model
            
            # Update configuration
            self.config.set("ai.default_provider", provider)
            self.config.set("ai.default_model", model)
            
            # Update context window
            await self._update_context_window()
            
            self.logger.info(f"Switched to {provider}/{model}")
            
        except Exception as e:
            self.logger.error(f"Failed to switch model: {e}")
            raise
    
    async def _update_context_window(self) -> None:
        """Update context window size based on current model."""
        # Model-specific context windows
        context_windows = {
            "deepseek-chat": 32000,
            "deepseek-coder": 16000,
            "gpt-3.5-turbo": 4096,
            "gpt-4": 8192,
            "gpt-4-turbo-preview": 128000,
            "claude-3-sonnet": 200000,
            "claude-3-opus": 200000,
            "claude-3-haiku": 200000,
            "gemini-pro": 32000,
        }
        
        self.max_context_tokens = context_windows.get(self.current_model, 8000)
    
    def _build_system_prompt(self) -> str:
        """Build the system prompt for autonomous behavior."""
        return """You are an advanced AI assistant integrated into a terminal environment. You have access to various tools and can help users with:

1. **File Operations**: Read, write, edit, and manage files and directories
2. **Shell Commands**: Execute system commands safely with user approval for dangerous operations
3. **Code Assistance**: Write, debug, format, and analyze code in multiple languages
4. **Git Operations**: Manage version control, commits, branches, and repositories
5. **Package Management**: Install, update, and manage dependencies
6. **System Monitoring**: Check system status, processes, and resource usage

**Guidelines:**
- Always be helpful, accurate, and safe
- Ask for clarification when requests are ambiguous
- Use tools appropriately and efficiently
- Explain your actions and reasoning
- Request approval for potentially dangerous operations
- Provide detailed explanations for complex tasks
- Suggest best practices and alternatives when appropriate

**Tool Usage:**
- Use tools to accomplish tasks rather than just describing how to do them
- Combine multiple tools when necessary to complete complex workflows
- Always validate inputs and handle errors gracefully
- Provide clear feedback on tool execution results

**Security:**
- Never execute commands that could harm the system
- Always request approval for file deletions, system changes, or network operations
- Validate file paths and command arguments
- Respect user permissions and system boundaries

You are autonomous and should take initiative to help users accomplish their goals efficiently and safely."""
    
    async def shutdown(self) -> None:
        """Shutdown the agent engine."""
        self.logger.info("Shutting down agent engine")
        self.is_processing = False
