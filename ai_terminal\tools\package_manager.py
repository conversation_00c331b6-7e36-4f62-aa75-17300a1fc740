"""
Package manager tool for AI Terminal.

Provides package management operations for multiple package managers
including pip, npm, cargo, and system package managers.
"""

from typing import Any, Dict

from ai_terminal.tools.base import BaseTool, ToolExecutionError


class PackageManagerTool(BaseTool):
    """Package management tool for multiple package managers."""
    
    def __init__(self):
        """Initialize package manager tool."""
        super().__init__(
            name="package_manager",
            description="Package management for pip, npm, cargo, and system packages"
        )
        self.requires_approval = True  # Package operations can be dangerous
    
    async def execute(self, manager: str, operation: str, **kwargs) -> Any:
        """Execute a package manager operation."""
        managers = {
            "pip": self._pip_operation,
            "npm": self._npm_operation,
            "cargo": self._cargo_operation,
            "apt": self._apt_operation,
            "brew": self._brew_operation,
        }
        
        if manager not in managers:
            raise ToolExecutionError(f"Unknown package manager: {manager}", self.name)
        
        return await managers[manager](operation, **kwargs)
    
    async def _pip_operation(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Execute pip operations."""
        try:
            import subprocess

            package = kwargs.get("package", "")
            version = kwargs.get("version", "")

            if operation == "install":
                if not package:
                    return {"error": "Package name required for install", "success": False}

                cmd = ["pip", "install"]
                if version:
                    cmd.append(f"{package}=={version}")
                else:
                    cmd.append(package)

                # Add common flags
                if kwargs.get("upgrade", False):
                    cmd.append("--upgrade")
                if kwargs.get("user", False):
                    cmd.append("--user")

            elif operation == "uninstall":
                if not package:
                    return {"error": "Package name required for uninstall", "success": False}
                cmd = ["pip", "uninstall", package, "-y"]

            elif operation == "list":
                cmd = ["pip", "list"]
                if kwargs.get("outdated", False):
                    cmd.append("--outdated")

            elif operation == "show":
                if not package:
                    return {"error": "Package name required for show", "success": False}
                cmd = ["pip", "show", package]

            elif operation == "search":
                if not package:
                    return {"error": "Search term required", "success": False}
                # Note: pip search was disabled, using alternative
                return {"error": "pip search is currently disabled", "success": False}

            elif operation == "upgrade":
                cmd = ["pip", "install", "--upgrade", "pip"]

            else:
                return {"error": f"Unknown pip operation: {operation}", "success": False}

            # Execute command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=120
            )

            return {
                "manager": "pip",
                "operation": operation,
                "success": result.returncode == 0,
                "output": result.stdout.strip(),
                "error": result.stderr.strip() if result.returncode != 0 else None,
                "package": package,
                "version": version
            }

        except subprocess.TimeoutExpired:
            return {"error": "Pip command timed out", "success": False, "manager": "pip"}
        except FileNotFoundError:
            return {"error": "pip not found in PATH", "success": False, "manager": "pip"}
        except Exception as e:
            self.logger.error(f"Pip operation failed: {e}")
            return {"error": str(e), "success": False, "manager": "pip"}
    
    async def _npm_operation(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Execute npm operations."""
        try:
            import subprocess

            package = kwargs.get("package", "")
            version = kwargs.get("version", "")

            if operation == "install":
                if not package:
                    return {"error": "Package name required for install", "success": False, "manager": "npm"}

                cmd = ["npm", "install"]
                if version:
                    cmd.append(f"{package}@{version}")
                else:
                    cmd.append(package)

                # Add flags
                if kwargs.get("global", False):
                    cmd.append("--global")
                if kwargs.get("save_dev", False):
                    cmd.append("--save-dev")
                if kwargs.get("save_exact", False):
                    cmd.append("--save-exact")

            elif operation == "uninstall":
                if not package:
                    return {"error": "Package name required for uninstall", "success": False, "manager": "npm"}
                cmd = ["npm", "uninstall", package]
                if kwargs.get("global", False):
                    cmd.append("--global")

            elif operation == "list":
                cmd = ["npm", "list"]
                if kwargs.get("global", False):
                    cmd.append("--global")
                if kwargs.get("depth"):
                    cmd.extend(["--depth", str(kwargs["depth"])])

            elif operation == "info":
                if not package:
                    return {"error": "Package name required for info", "success": False, "manager": "npm"}
                cmd = ["npm", "info", package]

            elif operation == "search":
                if not package:
                    return {"error": "Search term required", "success": False, "manager": "npm"}
                cmd = ["npm", "search", package]

            elif operation == "update":
                cmd = ["npm", "update"]
                if package:
                    cmd.append(package)
                if kwargs.get("global", False):
                    cmd.append("--global")

            else:
                return {"error": f"Unknown npm operation: {operation}", "success": False, "manager": "npm"}

            # Execute command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=120
            )

            return {
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr if result.returncode != 0 else None,
                "manager": "npm",
                "operation": operation,
                "package": package,
                "return_code": result.returncode
            }

        except subprocess.TimeoutExpired:
            return {"error": "npm command timed out", "success": False, "manager": "npm"}
        except Exception as e:
            self.logger.error(f"npm operation failed: {e}")
            return {"error": str(e), "success": False, "manager": "npm"}
    
    async def _cargo_operation(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Execute cargo operations."""
        try:
            import subprocess

            package = kwargs.get("package", "")
            version = kwargs.get("version", "")

            if operation == "install":
                if not package:
                    return {"error": "Package name required for install", "success": False, "manager": "cargo"}

                cmd = ["cargo", "install"]
                if version:
                    cmd.extend(["--version", version])
                cmd.append(package)

                # Add flags
                if kwargs.get("force", False):
                    cmd.append("--force")
                if kwargs.get("git"):
                    cmd.extend(["--git", kwargs["git"]])

            elif operation == "uninstall":
                if not package:
                    return {"error": "Package name required for uninstall", "success": False, "manager": "cargo"}
                cmd = ["cargo", "uninstall", package]

            elif operation == "search":
                if not package:
                    return {"error": "Search term required", "success": False, "manager": "cargo"}
                cmd = ["cargo", "search", package]
                if kwargs.get("limit"):
                    cmd.extend(["--limit", str(kwargs["limit"])])

            elif operation == "update":
                # Cargo doesn't have a direct update command for installed packages
                # This would typically be done by reinstalling with --force
                if package:
                    cmd = ["cargo", "install", "--force", package]
                else:
                    return {"error": "Package name required for update", "success": False, "manager": "cargo"}

            else:
                return {"error": f"Unknown cargo operation: {operation}", "success": False, "manager": "cargo"}

            # Execute command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # Cargo operations can take longer
            )

            return {
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr if result.returncode != 0 else None,
                "manager": "cargo",
                "operation": operation,
                "package": package,
                "return_code": result.returncode
            }

        except subprocess.TimeoutExpired:
            return {"error": "cargo command timed out", "success": False, "manager": "cargo"}
        except Exception as e:
            self.logger.error(f"Cargo operation failed: {e}")
            return {"error": str(e), "success": False, "manager": "cargo"}
    
    async def _apt_operation(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Execute apt operations."""
        try:
            import subprocess

            package = kwargs.get("package", "")

            if operation == "install":
                if not package:
                    return {"error": "Package name required for install", "success": False, "manager": "apt"}
                cmd = ["sudo", "apt", "install", "-y", package]

            elif operation == "remove":
                if not package:
                    return {"error": "Package name required for remove", "success": False, "manager": "apt"}
                cmd = ["sudo", "apt", "remove", "-y", package]
                if kwargs.get("purge", False):
                    cmd[2] = "purge"

            elif operation == "update":
                cmd = ["sudo", "apt", "update"]

            elif operation == "upgrade":
                cmd = ["sudo", "apt", "upgrade", "-y"]
                if package:
                    cmd.append(package)

            elif operation == "search":
                if not package:
                    return {"error": "Search term required", "success": False, "manager": "apt"}
                cmd = ["apt", "search", package]

            elif operation == "show":
                if not package:
                    return {"error": "Package name required for show", "success": False, "manager": "apt"}
                cmd = ["apt", "show", package]

            else:
                return {"error": f"Unknown apt operation: {operation}", "success": False, "manager": "apt"}

            # Execute command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # apt operations can take longer
            )

            return {
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr if result.returncode != 0 else None,
                "manager": "apt",
                "operation": operation,
                "package": package,
                "return_code": result.returncode
            }

        except subprocess.TimeoutExpired:
            return {"error": "apt command timed out", "success": False, "manager": "apt"}
        except Exception as e:
            self.logger.error(f"apt operation failed: {e}")
            return {"error": str(e), "success": False, "manager": "apt"}

    async def _brew_operation(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Execute brew operations."""
        try:
            import subprocess

            package = kwargs.get("package", "")

            if operation == "install":
                if not package:
                    return {"error": "Package name required for install", "success": False, "manager": "brew"}
                cmd = ["brew", "install", package]
                if kwargs.get("cask", False):
                    cmd.insert(2, "--cask")

            elif operation == "uninstall":
                if not package:
                    return {"error": "Package name required for uninstall", "success": False, "manager": "brew"}
                cmd = ["brew", "uninstall", package]
                if kwargs.get("cask", False):
                    cmd.insert(2, "--cask")

            elif operation == "update":
                cmd = ["brew", "update"]

            elif operation == "upgrade":
                cmd = ["brew", "upgrade"]
                if package:
                    cmd.append(package)

            elif operation == "search":
                if not package:
                    return {"error": "Search term required", "success": False, "manager": "brew"}
                cmd = ["brew", "search", package]

            elif operation == "info":
                if not package:
                    return {"error": "Package name required for info", "success": False, "manager": "brew"}
                cmd = ["brew", "info", package]

            else:
                return {"error": f"Unknown brew operation: {operation}", "success": False, "manager": "brew"}

            # Execute command
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # brew operations can take longer
            )

            return {
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr if result.returncode != 0 else None,
                "manager": "brew",
                "operation": operation,
                "package": package,
                "return_code": result.returncode
            }

        except subprocess.TimeoutExpired:
            return {"error": "brew command timed out", "success": False, "manager": "brew"}
        except Exception as e:
            self.logger.error(f"brew operation failed: {e}")
            return {"error": str(e), "success": False, "manager": "brew"}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get JSON schema for package manager tool."""
        return {
            "name": "package_manager",
            "description": "Package management operations",
            "parameters": {
                "type": "object",
                "properties": {
                    "manager": {
                        "type": "string",
                        "enum": ["pip", "npm", "cargo", "apt", "brew"],
                        "description": "Package manager to use"
                    },
                    "operation": {
                        "type": "string",
                        "enum": ["install", "uninstall", "list", "search", "update", "upgrade"],
                        "description": "Package operation to perform"
                    },
                    "package": {
                        "type": "string",
                        "description": "Package name for install/uninstall operations"
                    },
                    "version": {
                        "type": "string",
                        "description": "Specific package version"
                    }
                },
                "required": ["manager", "operation"]
            }
        }
