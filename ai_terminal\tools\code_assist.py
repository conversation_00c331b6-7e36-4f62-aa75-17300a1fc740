"""
Code assistance tool for AI Terminal.

Provides code analysis, formatting, linting, and debugging assistance
for multiple programming languages.
"""

from typing import Any, Dict

from ai_terminal.tools.base import BaseTool, ToolExecutionError


class CodeAssistTool(BaseTool):
    """Code assistance and analysis tool."""
    
    def __init__(self):
        """Initialize code assistance tool."""
        super().__init__(
            name="code_assist",
            description="Code analysis, formatting, linting, and debugging assistance"
        )
        self.requires_approval = False
    
    async def execute(self, operation: str, **kwargs) -> Any:
        """Execute a code assistance operation."""
        operations = {
            "format": self._format_code,
            "lint": self._lint_code,
            "analyze": self._analyze_code,
            "debug": self._debug_code,
            "test": self._run_tests,
            "docs": self._generate_docs,
        }
        
        if operation not in operations:
            raise ToolExecutionError(f"Unknown code operation: {operation}", self.name)
        
        return await operations[operation](**kwargs)
    
    async def _format_code(self, code: str, language: str = "python", **kwargs) -> Dict[str, Any]:
        """Format code using appropriate formatter."""
        try:
            if language.lower() == "python":
                return await self._format_python_code(code, **kwargs)
            elif language.lower() in ["javascript", "js", "typescript", "ts"]:
                return await self._format_js_code(code, **kwargs)
            elif language.lower() in ["json"]:
                return await self._format_json_code(code, **kwargs)
            else:
                # Basic formatting for other languages
                lines = code.splitlines()
                formatted_lines = []
                indent_level = 0

                for line in lines:
                    stripped = line.strip()
                    if not stripped:
                        formatted_lines.append("")
                        continue

                    # Simple indentation logic
                    if stripped.endswith(("{", ":", "(")):
                        formatted_lines.append("    " * indent_level + stripped)
                        indent_level += 1
                    elif stripped.startswith(("}", ")", "]")):
                        indent_level = max(0, indent_level - 1)
                        formatted_lines.append("    " * indent_level + stripped)
                    else:
                        formatted_lines.append("    " * indent_level + stripped)

                formatted_code = "\n".join(formatted_lines)
                changes = sum(1 for a, b in zip(code.splitlines(), formatted_lines) if a.strip() != b.strip())

                return {
                    "formatted_code": formatted_code,
                    "language": language,
                    "changes": changes,
                    "formatter": "basic"
                }

        except Exception as e:
            self.logger.error(f"Code formatting failed: {e}")
            return {
                "formatted_code": code,
                "language": language,
                "changes": 0,
                "error": str(e)
            }

    async def _format_python_code(self, code: str, **kwargs) -> Dict[str, Any]:
        """Format Python code using black."""
        try:
            import black

            # Format with black
            formatted_code = black.format_str(code, mode=black.FileMode())
            changes = sum(1 for a, b in zip(code.splitlines(), formatted_code.splitlines()) if a != b)

            return {
                "formatted_code": formatted_code,
                "language": "python",
                "changes": changes,
                "formatter": "black"
            }

        except ImportError:
            # Fallback to basic Python formatting
            return await self._basic_python_format(code)
        except Exception as e:
            self.logger.error(f"Black formatting failed: {e}")
            return await self._basic_python_format(code)

    async def _basic_python_format(self, code: str) -> Dict[str, Any]:
        """Basic Python code formatting."""
        lines = code.splitlines()
        formatted_lines = []
        indent_level = 0

        for line in lines:
            stripped = line.strip()
            if not stripped:
                formatted_lines.append("")
                continue

            # Handle dedentation
            if stripped.startswith(("except", "elif", "else", "finally")):
                current_indent = max(0, indent_level - 1)
            elif stripped.startswith(("return", "break", "continue", "pass", "raise")):
                current_indent = indent_level
            else:
                current_indent = indent_level

            formatted_lines.append("    " * current_indent + stripped)

            # Handle indentation
            if stripped.endswith(":") and not stripped.startswith("#"):
                indent_level += 1
            elif stripped in ["pass", "break", "continue"] or stripped.startswith("return"):
                indent_level = max(0, indent_level - 1)

        formatted_code = "\n".join(formatted_lines)
        changes = sum(1 for a, b in zip(code.splitlines(), formatted_lines) if a.strip() != b.strip())

        return {
            "formatted_code": formatted_code,
            "language": "python",
            "changes": changes,
            "formatter": "basic"
        }

    async def _format_js_code(self, code: str, **kwargs) -> Dict[str, Any]:
        """Format JavaScript/TypeScript code."""
        # Basic JS formatting
        lines = code.splitlines()
        formatted_lines = []
        indent_level = 0

        for line in lines:
            stripped = line.strip()
            if not stripped:
                formatted_lines.append("")
                continue

            if stripped.startswith(("}", ")", "]")):
                indent_level = max(0, indent_level - 1)
                formatted_lines.append("  " * indent_level + stripped)
            else:
                formatted_lines.append("  " * indent_level + stripped)

            if stripped.endswith(("{", "(")):
                indent_level += 1

        formatted_code = "\n".join(formatted_lines)
        changes = sum(1 for a, b in zip(code.splitlines(), formatted_lines) if a.strip() != b.strip())

        return {
            "formatted_code": formatted_code,
            "language": "javascript",
            "changes": changes,
            "formatter": "basic"
        }

    async def _format_json_code(self, code: str, **kwargs) -> Dict[str, Any]:
        """Format JSON code."""
        try:
            import json
            parsed = json.loads(code)
            formatted_code = json.dumps(parsed, indent=2, ensure_ascii=False)

            return {
                "formatted_code": formatted_code,
                "language": "json",
                "changes": 1 if code != formatted_code else 0,
                "formatter": "json"
            }
        except json.JSONDecodeError as e:
            return {
                "formatted_code": code,
                "language": "json",
                "changes": 0,
                "error": f"Invalid JSON: {e}"
            }

    async def _lint_code(self, code: str, language: str = "python", **kwargs) -> Dict[str, Any]:
        """Lint code and return issues."""
        try:
            if language.lower() == "python":
                return await self._lint_python_code(code, **kwargs)
            elif language.lower() in ["javascript", "js"]:
                return await self._lint_js_code(code, **kwargs)
            else:
                return await self._basic_lint(code, language)

        except Exception as e:
            self.logger.error(f"Code linting failed: {e}")
            return {
                "issues": [],
                "language": language,
                "score": 5,
                "error": str(e)
            }

    async def _lint_python_code(self, code: str, **kwargs) -> Dict[str, Any]:
        """Lint Python code using pylint."""
        issues = []
        score = 10

        try:
            # Basic Python syntax check
            compile(code, '<string>', 'exec')

            # Check for common issues
            lines = code.splitlines()
            for i, line in enumerate(lines, 1):
                # Check line length
                if len(line) > 88:
                    issues.append({
                        "line": i,
                        "column": 89,
                        "type": "warning",
                        "message": "Line too long (>88 characters)",
                        "rule": "line-too-long"
                    })

                # Check for unused imports (basic)
                if line.strip().startswith("import ") and line.strip() not in code[code.find(line)+len(line):]:
                    issues.append({
                        "line": i,
                        "column": 1,
                        "type": "warning",
                        "message": "Possibly unused import",
                        "rule": "unused-import"
                    })

            # Calculate score based on issues
            score = max(0, 10 - len(issues) * 0.5)

        except SyntaxError as e:
            issues.append({
                "line": e.lineno or 1,
                "column": e.offset or 1,
                "type": "error",
                "message": f"Syntax error: {e.msg}",
                "rule": "syntax-error"
            })
            score = 0

        return {
            "issues": issues,
            "language": "python",
            "score": score,
            "linter": "basic"
        }

    async def _lint_js_code(self, code: str, **kwargs) -> Dict[str, Any]:
        """Lint JavaScript code."""
        issues = []
        score = 10

        # Basic checks
        lines = code.splitlines()
        for i, line in enumerate(lines, 1):
            stripped = line.strip()

            # Check for missing semicolons
            if (stripped and not stripped.endswith((";", "{", "}", ")", "]")) and
                not stripped.startswith(("//", "/*", "*", "if", "for", "while", "function"))):
                issues.append({
                    "line": i,
                    "column": len(line),
                    "type": "warning",
                    "message": "Missing semicolon",
                    "rule": "missing-semicolon"
                })

            # Check for console.log
            if "console.log" in stripped:
                issues.append({
                    "line": i,
                    "column": line.find("console.log") + 1,
                    "type": "warning",
                    "message": "Avoid console.log in production code",
                    "rule": "no-console"
                })

        score = max(0, 10 - len(issues) * 0.5)

        return {
            "issues": issues,
            "language": "javascript",
            "score": score,
            "linter": "basic"
        }

    async def _basic_lint(self, code: str, language: str) -> Dict[str, Any]:
        """Basic linting for any language."""
        issues = []
        lines = code.splitlines()

        for i, line in enumerate(lines, 1):
            # Check for very long lines
            if len(line) > 120:
                issues.append({
                    "line": i,
                    "column": 121,
                    "type": "warning",
                    "message": "Line too long (>120 characters)",
                    "rule": "line-too-long"
                })

            # Check for trailing whitespace
            if line.endswith(" ") or line.endswith("\t"):
                issues.append({
                    "line": i,
                    "column": len(line),
                    "type": "info",
                    "message": "Trailing whitespace",
                    "rule": "trailing-whitespace"
                })

        score = max(0, 10 - len(issues) * 0.3)

        return {
            "issues": issues,
            "language": language,
            "score": score,
            "linter": "basic"
        }

    async def _analyze_code(self, code: str, language: str = "python", **kwargs) -> Dict[str, Any]:
        """Analyze code complexity and metrics."""
        try:
            lines = code.splitlines()
            non_empty_lines = [line for line in lines if line.strip()]

            # Basic metrics
            total_lines = len(lines)
            code_lines = len(non_empty_lines)
            comment_lines = len([line for line in lines if line.strip().startswith(("#", "//", "/*"))])
            blank_lines = total_lines - code_lines - comment_lines

            # Language-specific analysis
            if language.lower() == "python":
                analysis = await self._analyze_python_code(code)
            elif language.lower() in ["javascript", "js", "typescript", "ts"]:
                analysis = await self._analyze_js_code(code)
            else:
                analysis = await self._basic_code_analysis(code)

            # Combine metrics
            analysis.update({
                "total_lines": total_lines,
                "code_lines": code_lines,
                "comment_lines": comment_lines,
                "blank_lines": blank_lines,
                "language": language
            })

            return analysis

        except Exception as e:
            self.logger.error(f"Code analysis failed: {e}")
            return {
                "complexity": 1,
                "lines": len(code.splitlines()),
                "functions": 0,
                "classes": 0,
                "language": language,
                "error": str(e)
            }
    
    async def _analyze_python_code(self, code: str) -> Dict[str, Any]:
        """Analyze Python code for complexity and metrics."""
        import ast
        import re

        try:
            tree = ast.parse(code)

            # Count different elements
            functions = len([node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)])
            classes = len([node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)])
            imports = len([node for node in ast.walk(tree) if isinstance(node, (ast.Import, ast.ImportFrom))])

            # Calculate cyclomatic complexity (basic)
            complexity = 1  # Base complexity
            for node in ast.walk(tree):
                if isinstance(node, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                    complexity += 1
                elif isinstance(node, ast.BoolOp):
                    complexity += len(node.values) - 1

            # Maintainability index (simplified)
            lines_of_code = len([line for line in code.splitlines() if line.strip()])
            maintainability = max(0, 171 - 5.2 * complexity - 0.23 * lines_of_code)

            return {
                "complexity": complexity,
                "functions": functions,
                "classes": classes,
                "imports": imports,
                "maintainability_index": round(maintainability, 2),
                "analyzer": "ast"
            }

        except SyntaxError:
            return await self._basic_code_analysis(code)

    async def _analyze_js_code(self, code: str) -> Dict[str, Any]:
        """Analyze JavaScript code for complexity and metrics."""
        import re

        # Count functions
        function_patterns = [
            r'function\s+\w+\s*\(',
            r'\w+\s*:\s*function\s*\(',
            r'=>',
            r'function\s*\('
        ]
        functions = sum(len(re.findall(pattern, code)) for pattern in function_patterns)

        # Count classes
        classes = len(re.findall(r'class\s+\w+', code))

        # Count imports
        imports = len(re.findall(r'import\s+.*from|require\s*\(', code))

        # Basic complexity
        complexity = 1
        complexity_patterns = [
            r'\bif\b', r'\bwhile\b', r'\bfor\b', r'\btry\b', r'\bcatch\b',
            r'\bswitch\b', r'\bcase\b', r'&&', r'\|\|'
        ]
        for pattern in complexity_patterns:
            complexity += len(re.findall(pattern, code))

        return {
            "complexity": complexity,
            "functions": functions,
            "classes": classes,
            "imports": imports,
            "analyzer": "regex"
        }

    async def _basic_code_analysis(self, code: str) -> Dict[str, Any]:
        """Basic code analysis for any language."""
        import re

        lines = code.splitlines()

        # Count basic elements
        functions = len(re.findall(r'(function|def|fn)\s+\w+', code, re.IGNORECASE))
        classes = len(re.findall(r'(class|struct|interface)\s+\w+', code, re.IGNORECASE))

        # Basic complexity
        complexity = 1
        complexity_keywords = ['if', 'while', 'for', 'switch', 'case', 'try', 'catch']
        for keyword in complexity_keywords:
            complexity += len(re.findall(rf'\b{keyword}\b', code, re.IGNORECASE))

        return {
            "complexity": complexity,
            "functions": functions,
            "classes": classes,
            "analyzer": "basic"
        }

    async def _debug_code(self, code: str, error: str = None, **kwargs) -> Dict[str, Any]:
        """Debug code and suggest fixes."""
        suggestions = []
        error_type = "unknown"
        fixes = []

        try:
            if error:
                # Analyze the error message
                error_lower = error.lower()

                if "syntaxerror" in error_lower:
                    error_type = "syntax"
                    suggestions.append("Check for missing parentheses, brackets, or quotes")
                    suggestions.append("Verify proper indentation")
                    fixes.append("Run code through a syntax checker")

                elif "nameerror" in error_lower:
                    error_type = "name"
                    suggestions.append("Check if all variables are defined before use")
                    suggestions.append("Verify import statements")
                    fixes.append("Add missing variable definitions or imports")

                elif "typeerror" in error_lower:
                    error_type = "type"
                    suggestions.append("Check data types being used")
                    suggestions.append("Verify function arguments")
                    fixes.append("Add type checking or conversion")

                elif "indentationerror" in error_lower:
                    error_type = "indentation"
                    suggestions.append("Use consistent indentation (spaces or tabs)")
                    suggestions.append("Check for mixed indentation")
                    fixes.append("Reformat code with consistent indentation")

            # General code analysis for potential issues
            lines = code.splitlines()
            for i, line in enumerate(lines, 1):
                stripped = line.strip()

                # Check for common issues
                if stripped.endswith("="):
                    suggestions.append(f"Line {i}: Incomplete assignment")

                if "print(" in stripped and not stripped.endswith(")"):
                    suggestions.append(f"Line {i}: Possible unclosed print statement")

                if stripped.count("(") != stripped.count(")"):
                    suggestions.append(f"Line {i}: Mismatched parentheses")

                if stripped.count("[") != stripped.count("]"):
                    suggestions.append(f"Line {i}: Mismatched brackets")

            return {
                "suggestions": suggestions,
                "error_type": error_type,
                "fixes": fixes,
                "analyzed_lines": len(lines)
            }

        except Exception as e:
            self.logger.error(f"Debug analysis failed: {e}")
            return {
                "suggestions": ["Unable to analyze code"],
                "error_type": "analysis_error",
                "fixes": [],
                "error": str(e)
            }

    async def _run_tests(self, test_file: str = None, **kwargs) -> Dict[str, Any]:
        """Run tests and return results."""
        try:
            import subprocess
            import os

            if not test_file:
                # Look for common test files
                test_patterns = ["test_*.py", "*_test.py", "tests.py"]
                for pattern in test_patterns:
                    import glob
                    matches = glob.glob(pattern)
                    if matches:
                        test_file = matches[0]
                        break

            if not test_file or not os.path.exists(test_file):
                return {
                    "passed": 0,
                    "failed": 0,
                    "total": 0,
                    "coverage": 0,
                    "error": "No test file found"
                }

            # Try to run pytest first, then unittest
            try:
                result = subprocess.run(
                    ["python", "-m", "pytest", test_file, "-v", "--tb=short"],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                # Parse pytest output
                output = result.stdout + result.stderr
                passed = output.count(" PASSED")
                failed = output.count(" FAILED")
                total = passed + failed

                return {
                    "passed": passed,
                    "failed": failed,
                    "total": total,
                    "coverage": 0,  # Would need coverage.py for this
                    "output": output[:1000],  # Limit output
                    "runner": "pytest"
                }

            except (subprocess.TimeoutExpired, FileNotFoundError):
                # Fallback to unittest
                result = subprocess.run(
                    ["python", "-m", "unittest", test_file],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                output = result.stdout + result.stderr
                # Basic parsing for unittest
                if "OK" in output:
                    passed = 1
                    failed = 0
                else:
                    passed = 0
                    failed = 1

                return {
                    "passed": passed,
                    "failed": failed,
                    "total": passed + failed,
                    "coverage": 0,
                    "output": output[:1000],
                    "runner": "unittest"
                }

        except Exception as e:
            self.logger.error(f"Test execution failed: {e}")
            return {
                "passed": 0,
                "failed": 0,
                "total": 0,
                "coverage": 0,
                "error": str(e)
            }

    async def _generate_docs(self, code: str, language: str = "python", **kwargs) -> Dict[str, Any]:
        """Generate documentation for code."""
        try:
            if language.lower() == "python":
                return await self._generate_python_docs(code)
            elif language.lower() in ["javascript", "js"]:
                return await self._generate_js_docs(code)
            else:
                return await self._generate_basic_docs(code, language)

        except Exception as e:
            self.logger.error(f"Documentation generation failed: {e}")
            return {
                "documentation": "",
                "language": language,
                "error": str(e)
            }

    async def _generate_python_docs(self, code: str) -> Dict[str, Any]:
        """Generate Python documentation."""
        import ast
        import re

        try:
            tree = ast.parse(code)
            docs = []

            # Module docstring
            if (ast.get_docstring(tree)):
                docs.append(f"# Module Documentation\n\n{ast.get_docstring(tree)}\n")

            # Classes
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    docs.append(f"## Class: {node.name}\n")
                    if ast.get_docstring(node):
                        docs.append(f"{ast.get_docstring(node)}\n")

                    # Methods
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            docs.append(f"### Method: {item.name}\n")
                            if ast.get_docstring(item):
                                docs.append(f"{ast.get_docstring(item)}\n")
                            else:
                                # Generate basic documentation
                                args = [arg.arg for arg in item.args.args if arg.arg != 'self']
                                docs.append(f"Method with arguments: {', '.join(args) if args else 'none'}\n")

                elif isinstance(node, ast.FunctionDef) and not any(isinstance(parent, ast.ClassDef) for parent in ast.walk(tree)):
                    # Top-level functions
                    docs.append(f"## Function: {node.name}\n")
                    if ast.get_docstring(node):
                        docs.append(f"{ast.get_docstring(node)}\n")
                    else:
                        args = [arg.arg for arg in node.args.args]
                        docs.append(f"Function with arguments: {', '.join(args) if args else 'none'}\n")

            documentation = "\n".join(docs) if docs else "No documentation found in code."

            return {
                "documentation": documentation,
                "language": "python",
                "format": "markdown"
            }

        except SyntaxError:
            return await self._generate_basic_docs(code, "python")

    async def _generate_js_docs(self, code: str) -> Dict[str, Any]:
        """Generate JavaScript documentation."""
        import re

        docs = []

        # Find functions
        function_patterns = [
            (r'function\s+(\w+)\s*\(([^)]*)\)', "Function"),
            (r'(\w+)\s*:\s*function\s*\(([^)]*)\)', "Method"),
            (r'const\s+(\w+)\s*=\s*\(([^)]*)\)\s*=>', "Arrow Function"),
            (r'(\w+)\s*=\s*\(([^)]*)\)\s*=>', "Arrow Function")
        ]

        for pattern, func_type in function_patterns:
            matches = re.finditer(pattern, code)
            for match in matches:
                name = match.group(1)
                params = match.group(2).strip()
                docs.append(f"## {func_type}: {name}\n")
                docs.append(f"Parameters: {params if params else 'none'}\n")

        # Find classes
        class_matches = re.finditer(r'class\s+(\w+)', code)
        for match in class_matches:
            name = match.group(1)
            docs.append(f"## Class: {name}\n")

        documentation = "\n".join(docs) if docs else "No functions or classes found."

        return {
            "documentation": documentation,
            "language": "javascript",
            "format": "markdown"
        }

    async def _generate_basic_docs(self, code: str, language: str) -> Dict[str, Any]:
        """Generate basic documentation for any language."""
        import re

        docs = [f"# {language.title()} Code Documentation\n"]

        # Find functions (basic pattern)
        function_pattern = r'(function|def|fn|func)\s+(\w+)'
        matches = re.finditer(function_pattern, code, re.IGNORECASE)

        functions = []
        for match in matches:
            functions.append(match.group(2))

        if functions:
            docs.append("## Functions\n")
            for func in functions:
                docs.append(f"- {func}")

        # Find classes (basic pattern)
        class_pattern = r'(class|struct|interface)\s+(\w+)'
        matches = re.finditer(class_pattern, code, re.IGNORECASE)

        classes = []
        for match in matches:
            classes.append(match.group(2))

        if classes:
            docs.append("\n## Classes/Types\n")
            for cls in classes:
                docs.append(f"- {cls}")

        documentation = "\n".join(docs)

        return {
            "documentation": documentation,
            "language": language,
            "format": "markdown"
        }
    
    def get_schema(self) -> Dict[str, Any]:
        """Get JSON schema for code assistance tool."""
        return {
            "name": "code_assist",
            "description": "Code analysis, formatting, and assistance",
            "parameters": {
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": ["format", "lint", "analyze", "debug", "test", "docs"],
                        "description": "Code assistance operation to perform"
                    },
                    "code": {
                        "type": "string",
                        "description": "Code to analyze or format"
                    },
                    "language": {
                        "type": "string",
                        "description": "Programming language"
                    },
                    "file": {
                        "type": "string",
                        "description": "File path for file-based operations"
                    }
                },
                "required": ["operation"]
            }
        }
