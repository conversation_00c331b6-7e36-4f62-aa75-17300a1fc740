"""
Google Gemini provider implementation for AI Terminal.

Provides integration with Google's Gemini models with support
for advanced reasoning and multimodal capabilities.
"""

from typing import Any, Dict, List, Optional

import google.generativeai as genai

from ai_terminal.providers.base import BaseProvider, ProviderError, AuthenticationError


class GeminiProvider(BaseProvider):
    """Google Gemini provider implementation."""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """Initialize Gemini provider."""
        super().__init__(name, config)
        self.client = None
        self.default_model = config.get("model", "gemini-pro")
    
    async def initialize(self) -> None:
        """Initialize the Gemini provider."""
        try:
            if not self.api_key:
                raise AuthenticationError("Google API key is required", self.name)
            
            genai.configure(api_key=self.api_key)
            self.client = genai.GenerativeModel(self.default_model)
            
            await self.validate_connection()
            self.is_initialized = True
            self.logger.info("Gemini provider initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Gemini provider: {e}")
            raise
    
    async def generate_response(
        self,
        messages: List[Dict[str, Any]],
        model: Optional[str] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate response from Gemini API."""
        if not self.client:
            raise ProviderError("Provider not initialized", self.name)
        
        try:
            # Convert messages to Gemini format
            prompt = self._messages_to_prompt(messages)
            
            generation_config = genai.types.GenerationConfig(
                max_output_tokens=kwargs.get("max_tokens", self.max_tokens),
                temperature=kwargs.get("temperature", self.temperature),
            )
            
            if stream:
                response = self.client.generate_content(
                    prompt,
                    generation_config=generation_config,
                    stream=True
                )
                return {"stream": self._handle_streaming_response(response)}
            else:
                response = self.client.generate_content(
                    prompt,
                    generation_config=generation_config
                )
                return self.parse_response(response)
                
        except Exception as e:
            self.logger.error(f"Gemini API error: {e}")
            raise ProviderError(f"API request failed: {e}", self.name)
    
    def _messages_to_prompt(self, messages: List[Dict[str, Any]]) -> str:
        """Convert messages to Gemini prompt format."""
        prompt_parts = []
        
        for message in messages:
            role = message["role"]
            content = message["content"]
            
            if role == "system":
                prompt_parts.append(f"Instructions: {content}")
            elif role == "user":
                prompt_parts.append(f"User: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
        
        return "\n\n".join(prompt_parts)
    
    async def _handle_streaming_response(self, response):
        """Handle streaming response from Gemini."""
        for chunk in response:
            yield self.parse_streaming_chunk(chunk)
    
    def parse_response(self, response) -> Dict[str, Any]:
        """Parse Gemini API response."""
        content = ""
        if response.candidates:
            candidate = response.candidates[0]
            if candidate.content and candidate.content.parts:
                content = candidate.content.parts[0].text
        
        return {
            "content": content,
            "tool_calls": [],  # Gemini function calling format differs
            "usage": {
                "prompt_tokens": getattr(response.usage_metadata, 'prompt_token_count', 0),
                "completion_tokens": getattr(response.usage_metadata, 'candidates_token_count', 0),
                "total_tokens": getattr(response.usage_metadata, 'total_token_count', 0),
            },
            "model": self.default_model,
            "provider": self.name,
            "finish_reason": "stop",
        }
    
    def parse_streaming_chunk(self, chunk) -> Dict[str, Any]:
        """Parse Gemini streaming chunk."""
        content = ""
        if chunk.candidates:
            candidate = chunk.candidates[0]
            if candidate.content and candidate.content.parts:
                content = candidate.content.parts[0].text
        
        return {
            "content": content,
            "tool_calls": [],
            "finish_reason": None,
            "model": self.default_model,
            "provider": self.name,
        }
    
    async def validate_connection(self) -> bool:
        """Validate connection to Gemini API."""
        try:
            if not self.client:
                return False
            
            response = self.client.generate_content("Hello")
            return bool(response.candidates)
            
        except Exception as e:
            self.logger.error(f"Gemini connection validation failed: {e}")
            return False
    
    async def list_models(self) -> List[str]:
        """List available Gemini models."""
        try:
            models = genai.list_models()
            return [model.name.split('/')[-1] for model in models if 'generateContent' in model.supported_generation_methods]
        except Exception as e:
            self.logger.error(f"Failed to list Gemini models: {e}")
            return ["gemini-pro", "gemini-pro-vision"]
    
    async def get_model_info(self, model: str) -> Dict[str, Any]:
        """Get information about a Gemini model."""
        model_info = {
            "gemini-pro": {
                "name": "Gemini Pro",
                "context_window": 32000,
                "supports_function_calling": True,
            },
            "gemini-pro-vision": {
                "name": "Gemini Pro Vision",
                "context_window": 32000,
                "supports_function_calling": True,
                "supports_vision": True,
            },
        }
        
        return model_info.get(model, {"name": model, "context_window": 32000})
    
    def supports_vision(self) -> bool:
        """Gemini Pro Vision supports vision."""
        return "vision" in self.model.lower()
    
    async def shutdown(self) -> None:
        """Shutdown the Gemini provider."""
        self.client = None
        await super().shutdown()
