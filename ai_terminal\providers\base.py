"""
Base provider interface for AI Terminal.

Defines the common interface that all AI providers must implement
for unified interaction across different AI services.
"""

from abc import ABC, abstractmethod
from typing import Any, AsyncIterator, Dict, List, Optional

from ai_terminal.core.logger import LoggerMixin


class BaseProvider(ABC, LoggerMixin):
    """
    Base class for all AI providers.
    
    Defines the common interface that all providers must implement
    to ensure consistent behavior across different AI services.
    """
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """Initialize the provider."""
        self.name = name
        self.config = config
        self.api_key = config.get("api_key")
        self.base_url = config.get("base_url")
        self.model = config.get("model")
        self.max_tokens = config.get("max_tokens", 4096)
        self.temperature = config.get("temperature", 0.7)
        self.is_initialized = False
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the provider."""
        pass
    
    @abstractmethod
    async def generate_response(
        self,
        messages: List[Dict[str, Any]],
        model: Optional[str] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a response from the AI model.
        
        Args:
            messages: List of conversation messages
            model: Model to use (optional, uses default if not specified)
            tools: Available tools for function calling
            stream: Whether to stream the response
            **kwargs: Additional provider-specific parameters
            
        Returns:
            Response dictionary with content, usage, and tool calls
        """
        pass
    
    @abstractmethod
    async def validate_connection(self) -> bool:
        """Validate the connection to the AI service."""
        pass
    
    @abstractmethod
    async def list_models(self) -> List[str]:
        """List available models for this provider."""
        pass
    
    @abstractmethod
    async def get_model_info(self, model: str) -> Dict[str, Any]:
        """Get information about a specific model."""
        pass
    
    async def shutdown(self) -> None:
        """Shutdown the provider and cleanup resources."""
        self.is_initialized = False
        self.logger.debug(f"Provider {self.name} shutdown")
    
    def supports_streaming(self) -> bool:
        """Check if the provider supports streaming responses."""
        return True
    
    def supports_function_calling(self) -> bool:
        """Check if the provider supports function calling."""
        return True
    
    def supports_vision(self) -> bool:
        """Check if the provider supports vision/image inputs."""
        return False
    
    def get_context_window(self, model: Optional[str] = None) -> int:
        """Get the context window size for a model."""
        model = model or self.model
        
        # Default context windows for common models
        context_windows = {
            "gpt-3.5-turbo": 4096,
            "gpt-4": 8192,
            "gpt-4-turbo-preview": 128000,
            "claude-3-sonnet": 200000,
            "claude-3-opus": 200000,
            "claude-3-haiku": 200000,
            "deepseek-chat": 32000,
            "deepseek-coder": 16000,
            "gemini-pro": 32000,
        }
        
        return context_windows.get(model, 8000)
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation)."""
        # Simple estimation: ~4 characters per token
        return len(text) // 4
    
    def format_messages(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format messages for this provider (override if needed)."""
        return messages
    
    def format_tools(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format tools for this provider (override if needed)."""
        return tools
    
    def parse_response(self, response: Any) -> Dict[str, Any]:
        """Parse provider response into standard format."""
        return {
            "content": "",
            "tool_calls": [],
            "usage": {
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0,
            },
            "model": self.model,
            "provider": self.name,
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform a health check on the provider."""
        try:
            is_healthy = await self.validate_connection()
            return {
                "provider": self.name,
                "healthy": is_healthy,
                "model": self.model,
                "supports_streaming": self.supports_streaming(),
                "supports_function_calling": self.supports_function_calling(),
                "supports_vision": self.supports_vision(),
                "context_window": self.get_context_window(),
            }
        except Exception as e:
            return {
                "provider": self.name,
                "healthy": False,
                "error": str(e),
            }


class StreamingResponse:
    """Base class for streaming responses."""
    
    def __init__(self, provider: BaseProvider):
        """Initialize streaming response."""
        self.provider = provider
        self._stream = None
    
    async def __aiter__(self) -> AsyncIterator[Dict[str, Any]]:
        """Async iterator for streaming chunks."""
        if self._stream:
            async for chunk in self._stream:
                yield self.provider.parse_response(chunk)
    
    def stream(self) -> AsyncIterator[Dict[str, Any]]:
        """Get the stream iterator."""
        return self.__aiter__()


class ProviderError(Exception):
    """Base exception for provider errors."""
    
    def __init__(self, message: str, provider: str, error_code: Optional[str] = None):
        """Initialize provider error."""
        super().__init__(message)
        self.provider = provider
        self.error_code = error_code


class AuthenticationError(ProviderError):
    """Authentication error with provider."""
    pass


class RateLimitError(ProviderError):
    """Rate limit exceeded error."""
    pass


class ModelNotFoundError(ProviderError):
    """Model not found error."""
    pass


class InvalidRequestError(ProviderError):
    """Invalid request error."""
    pass
