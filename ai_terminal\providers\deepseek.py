"""
DeepSeek AI provider implementation for AI Terminal.

Provides integration with DeepSeek's AI models including DeepSeek-Chat
and DeepSeek-Coder with support for streaming and function calling.
"""

import json
from typing import Any, AsyncIterator, Dict, List, Optional

import aiohttp

from ai_terminal.providers.base import (
    BaseProvider, StreamingResponse, ProviderError, 
    AuthenticationError, RateLimitError, ModelNotFoundError
)


class DeepSeekProvider(BaseProvider):
    """
    DeepSeek AI provider implementation.
    
    Supports DeepSeek-Chat and DeepSeek-Coder models with streaming
    responses and function calling capabilities.
    """
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """Initialize DeepSeek provider."""
        super().__init__(name, config)
        self.base_url = config.get("base_url", "https://api.deepseek.com")
        self.session: Optional[aiohttp.ClientSession] = None
        
        # DeepSeek specific settings
        self.default_model = config.get("model", "deepseek-chat")
        self.max_retries = config.get("max_retries", 3)
        self.timeout = config.get("timeout", 60)
    
    async def initialize(self) -> None:
        """Initialize the DeepSeek provider."""
        try:
            if not self.api_key:
                raise AuthenticationError("DeepSeek API key is required", self.name)
            
            # Create HTTP session
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json",
                    "User-Agent": "AI-Terminal/1.0"
                }
            )
            
            # Validate connection
            await self.validate_connection()
            
            self.is_initialized = True
            self.logger.info("DeepSeek provider initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize DeepSeek provider: {e}")
            raise
    
    async def generate_response(
        self,
        messages: List[Dict[str, Any]],
        model: Optional[str] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate response from DeepSeek API."""
        if not self.is_initialized or not self.session:
            raise ProviderError("Provider not initialized", self.name)
        
        model = model or self.default_model
        
        # Prepare request payload
        payload = {
            "model": model,
            "messages": self.format_messages(messages),
            "max_tokens": kwargs.get("max_tokens", self.max_tokens),
            "temperature": kwargs.get("temperature", self.temperature),
            "stream": stream,
        }
        
        # Add tools if provided
        if tools:
            payload["tools"] = self.format_tools(tools)
            payload["tool_choice"] = "auto"
        
        try:
            if stream:
                return await self._generate_streaming_response(payload)
            else:
                return await self._generate_single_response(payload)
                
        except Exception as e:
            self.logger.error(f"DeepSeek API error: {e}")
            raise ProviderError(f"API request failed: {e}", self.name)
    
    async def _generate_single_response(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a single (non-streaming) response."""
        url = f"{self.base_url}/v1/chat/completions"
        
        async with self.session.post(url, json=payload) as response:
            if response.status == 401:
                raise AuthenticationError("Invalid API key", self.name)
            elif response.status == 429:
                raise RateLimitError("Rate limit exceeded", self.name)
            elif response.status == 404:
                raise ModelNotFoundError(f"Model {payload['model']} not found", self.name)
            elif response.status != 200:
                error_text = await response.text()
                raise ProviderError(f"API error {response.status}: {error_text}", self.name)
            
            data = await response.json()
            return self.parse_response(data)
    
    async def _generate_streaming_response(self, payload: Dict[str, Any]) -> StreamingResponse:
        """Generate a streaming response."""
        url = f"{self.base_url}/v1/chat/completions"
        
        class DeepSeekStreamingResponse(StreamingResponse):
            def __init__(self, provider, session, url, payload):
                super().__init__(provider)
                self.session = session
                self.url = url
                self.payload = payload
            
            async def __aiter__(self) -> AsyncIterator[Dict[str, Any]]:
                async with self.session.post(self.url, json=self.payload) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise ProviderError(f"Streaming error {response.status}: {error_text}", self.provider.name)
                    
                    async for line in response.content:
                        line = line.decode('utf-8').strip()
                        if line.startswith('data: '):
                            data_str = line[6:]  # Remove 'data: ' prefix
                            
                            if data_str == '[DONE]':
                                break
                            
                            try:
                                chunk_data = json.loads(data_str)
                                yield self.provider.parse_streaming_chunk(chunk_data)
                            except json.JSONDecodeError:
                                continue
        
        return DeepSeekStreamingResponse(self, self.session, url, payload)
    
    def parse_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse DeepSeek API response."""
        choice = response.get("choices", [{}])[0]
        message = choice.get("message", {})
        
        # Extract content
        content = message.get("content", "")
        
        # Extract tool calls
        tool_calls = []
        if message.get("tool_calls"):
            for tool_call in message["tool_calls"]:
                tool_calls.append({
                    "id": tool_call.get("id"),
                    "type": tool_call.get("type", "function"),
                    "function": {
                        "name": tool_call.get("function", {}).get("name"),
                        "arguments": tool_call.get("function", {}).get("arguments")
                    }
                })
        
        # Extract usage information
        usage = response.get("usage", {})
        
        return {
            "content": content,
            "tool_calls": tool_calls,
            "usage": {
                "prompt_tokens": usage.get("prompt_tokens", 0),
                "completion_tokens": usage.get("completion_tokens", 0),
                "total_tokens": usage.get("total_tokens", 0),
            },
            "model": response.get("model", self.default_model),
            "provider": self.name,
            "finish_reason": choice.get("finish_reason"),
        }
    
    def parse_streaming_chunk(self, chunk: Dict[str, Any]) -> Dict[str, Any]:
        """Parse a streaming response chunk."""
        choice = chunk.get("choices", [{}])[0]
        delta = choice.get("delta", {})
        
        return {
            "content": delta.get("content", ""),
            "tool_calls": delta.get("tool_calls", []),
            "finish_reason": choice.get("finish_reason"),
            "model": chunk.get("model", self.default_model),
            "provider": self.name,
        }
    
    def format_tools(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format tools for DeepSeek API."""
        formatted_tools = []
        
        for tool in tools:
            formatted_tool = {
                "type": "function",
                "function": {
                    "name": tool.get("name"),
                    "description": tool.get("description", ""),
                    "parameters": tool.get("parameters", {})
                }
            }
            formatted_tools.append(formatted_tool)
        
        return formatted_tools
    
    async def validate_connection(self) -> bool:
        """Validate connection to DeepSeek API."""
        try:
            if not self.session:
                return False
            
            # Test with a simple request
            test_payload = {
                "model": self.default_model,
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 10
            }
            
            url = f"{self.base_url}/v1/chat/completions"
            async with self.session.post(url, json=test_payload) as response:
                return response.status == 200
                
        except Exception as e:
            self.logger.error(f"DeepSeek connection validation failed: {e}")
            return False
    
    async def list_models(self) -> List[str]:
        """List available DeepSeek models."""
        try:
            if not self.session:
                return []
            
            url = f"{self.base_url}/v1/models"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return [model["id"] for model in data.get("data", [])]
                else:
                    # Return known models if API doesn't support listing
                    return ["deepseek-chat", "deepseek-coder"]
                    
        except Exception as e:
            self.logger.error(f"Failed to list DeepSeek models: {e}")
            return ["deepseek-chat", "deepseek-coder"]
    
    async def get_model_info(self, model: str) -> Dict[str, Any]:
        """Get information about a DeepSeek model."""
        model_info = {
            "deepseek-chat": {
                "name": "DeepSeek Chat",
                "description": "General-purpose conversational AI model",
                "context_window": 32000,
                "supports_function_calling": True,
                "supports_streaming": True,
            },
            "deepseek-coder": {
                "name": "DeepSeek Coder",
                "description": "Specialized coding and programming model",
                "context_window": 16000,
                "supports_function_calling": True,
                "supports_streaming": True,
            }
        }
        
        return model_info.get(model, {
            "name": model,
            "description": "Unknown DeepSeek model",
            "context_window": 8000,
            "supports_function_calling": True,
            "supports_streaming": True,
        })
    
    def get_context_window(self, model: Optional[str] = None) -> int:
        """Get context window size for DeepSeek models."""
        model = model or self.default_model
        
        context_windows = {
            "deepseek-chat": 32000,
            "deepseek-coder": 16000,
        }
        
        return context_windows.get(model, 8000)
    
    async def shutdown(self) -> None:
        """Shutdown the DeepSeek provider."""
        if self.session:
            await self.session.close()
            self.session = None
        
        await super().shutdown()
