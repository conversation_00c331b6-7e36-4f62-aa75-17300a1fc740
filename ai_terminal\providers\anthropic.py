"""
Anthropic provider implementation for AI Terminal.

Provides integration with Anthropic's <PERSON> models with support
for streaming and advanced reasoning capabilities.
"""

from typing import Any, Dict, List, Optional

import anthropic

from ai_terminal.providers.base import BaseProvider, ProviderError, AuthenticationError


class AnthropicProvider(BaseProvider):
    """Anthropic Claude provider implementation."""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """Initialize Anthropic provider."""
        super().__init__(name, config)
        self.client: Optional[anthropic.AsyncAnthropic] = None
        self.default_model = config.get("model", "claude-3-sonnet-20240229")
    
    async def initialize(self) -> None:
        """Initialize the Anthropic provider."""
        try:
            if not self.api_key:
                raise AuthenticationError("Anthropic API key is required", self.name)
            
            self.client = anthropic.AsyncAnthropic(
                api_key=self.api_key,
                base_url=self.base_url
            )
            
            await self.validate_connection()
            self.is_initialized = True
            self.logger.info("Anthropic provider initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Anthropic provider: {e}")
            raise
    
    async def generate_response(
        self,
        messages: List[Dict[str, Any]],
        model: Optional[str] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate response from Anthropic API."""
        if not self.client:
            raise ProviderError("Provider not initialized", self.name)
        
        model = model or self.default_model
        
        try:
            # Convert messages to Anthropic format
            system_message = ""
            formatted_messages = []
            
            for msg in messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                else:
                    formatted_messages.append(msg)
            
            params = {
                "model": model,
                "messages": formatted_messages,
                "max_tokens": kwargs.get("max_tokens", self.max_tokens),
                "temperature": kwargs.get("temperature", self.temperature),
                "stream": stream,
            }
            
            if system_message:
                params["system"] = system_message
            
            if tools:
                params["tools"] = self.format_tools(tools)
            
            response = await self.client.messages.create(**params)
            
            if stream:
                return {"stream": self._handle_streaming_response(response)}
            else:
                return self.parse_response(response)
                
        except Exception as e:
            self.logger.error(f"Anthropic API error: {e}")
            raise ProviderError(f"API request failed: {e}", self.name)
    
    async def _handle_streaming_response(self, response):
        """Handle streaming response from Anthropic."""
        async for chunk in response:
            yield self.parse_streaming_chunk(chunk)
    
    def parse_response(self, response) -> Dict[str, Any]:
        """Parse Anthropic API response."""
        content = ""
        tool_calls = []
        
        for content_block in response.content:
            if content_block.type == "text":
                content += content_block.text
            elif content_block.type == "tool_use":
                tool_calls.append({
                    "id": content_block.id,
                    "type": "function",
                    "function": {
                        "name": content_block.name,
                        "arguments": content_block.input
                    }
                })
        
        return {
            "content": content,
            "tool_calls": tool_calls,
            "usage": {
                "prompt_tokens": response.usage.input_tokens,
                "completion_tokens": response.usage.output_tokens,
                "total_tokens": response.usage.input_tokens + response.usage.output_tokens,
            },
            "model": response.model,
            "provider": self.name,
            "finish_reason": response.stop_reason,
        }
    
    def parse_streaming_chunk(self, chunk) -> Dict[str, Any]:
        """Parse streaming response chunk."""
        content = ""
        tool_calls = []
        
        if hasattr(chunk, 'delta') and chunk.delta:
            if hasattr(chunk.delta, 'text'):
                content = chunk.delta.text
        
        return {
            "content": content,
            "tool_calls": tool_calls,
            "finish_reason": getattr(chunk, 'stop_reason', None),
            "model": self.default_model,
            "provider": self.name,
        }
    
    def format_tools(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format tools for Anthropic API."""
        formatted_tools = []
        
        for tool in tools:
            formatted_tool = {
                "name": tool.get("name"),
                "description": tool.get("description", ""),
                "input_schema": tool.get("parameters", {})
            }
            formatted_tools.append(formatted_tool)
        
        return formatted_tools
    
    async def validate_connection(self) -> bool:
        """Validate connection to Anthropic API."""
        try:
            if not self.client:
                return False
            
            response = await self.client.messages.create(
                model=self.default_model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            return bool(response)
            
        except Exception as e:
            self.logger.error(f"Anthropic connection validation failed: {e}")
            return False
    
    async def list_models(self) -> List[str]:
        """List available Anthropic models."""
        return [
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229", 
            "claude-3-haiku-20240307"
        ]
    
    async def get_model_info(self, model: str) -> Dict[str, Any]:
        """Get information about an Anthropic model."""
        model_info = {
            "claude-3-opus-20240229": {
                "name": "Claude 3 Opus",
                "context_window": 200000,
                "supports_function_calling": True,
            },
            "claude-3-sonnet-20240229": {
                "name": "Claude 3 Sonnet", 
                "context_window": 200000,
                "supports_function_calling": True,
            },
            "claude-3-haiku-20240307": {
                "name": "Claude 3 Haiku",
                "context_window": 200000,
                "supports_function_calling": True,
            },
        }
        
        return model_info.get(model, {"name": model, "context_window": 200000})
    
    def supports_vision(self) -> bool:
        """Claude 3 models support vision."""
        return "claude-3" in self.model.lower()
    
    async def shutdown(self) -> None:
        """Shutdown the Anthropic provider."""
        if self.client:
            await self.client.aclose()
            self.client = None
        await super().shutdown()
