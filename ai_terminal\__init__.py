"""
AI Terminal - Sophisticated autonomous AI-powered CLI terminal application.

A production-ready Python application with modern architecture, advanced features,
and enterprise-grade quality for autonomous AI assistance in terminal environments.
"""

__version__ = "1.0.0"
__author__ = "AI Terminal Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

from ai_terminal.core.app import AITerminalApp
from ai_terminal.core.config import Config
from ai_terminal.agents.engine import AgentEngine
from ai_terminal.tools.manager import ToolManager

__all__ = [
    "AITerminalApp",
    "Config", 
    "AgentEngine",
    "ToolManager",
    "__version__",
]
