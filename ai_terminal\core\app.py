"""
Main application orchestrator for AI Terminal.

Coordinates all components including UI, agents, tools, and providers
to deliver a cohesive autonomous AI terminal experience.
"""

import asyncio
import signal
import sys
from typing import Optional

from ai_terminal.agents.engine import AgentEngine
from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin
from ai_terminal.providers.manager import ProviderManager
from ai_terminal.security.validator import SecurityValidator
from ai_terminal.security.approval import Approval<PERSON>anager
from ai_terminal.storage.database import DatabaseManager
from ai_terminal.storage.session import SessionManager
from ai_terminal.tools.manager import ToolManager
from ai_terminal.ui.terminal import TerminalApp


class AITerminalApp(LoggerMixin):
    """
    Main application class that orchestrates all components.
    
    This class is responsible for:
    - Initializing all subsystems
    - Managing application lifecycle
    - Coordinating between components
    - Handling graceful shutdown
    """
    
    def __init__(self, config: Config):
        """Initialize the AI Terminal application."""
        self.config = config
        self.running = False
        self.work_context = None

        # Initialize core components
        self.db_manager: Optional[DatabaseManager] = None
        self.session_manager: Optional[SessionManager] = None
        self.provider_manager: Optional[ProviderManager] = None
        self.tool_manager: Optional[ToolManager] = None
        self.agent_engine: Optional[AgentEngine] = None
        self.terminal_app: Optional[TerminalApp] = None

        # Security components
        self.security_validator: Optional[SecurityValidator] = None
        self.approval_manager: Optional[ApprovalManager] = None
        
        self.logger.info("AI Terminal application initialized")
    
    async def initialize(self) -> None:
        """Initialize all application components."""
        try:
            self.logger.info("Initializing AI Terminal components...")
            
            # Initialize database
            self.db_manager = DatabaseManager(self.config)
            await self.db_manager.initialize()
            self.logger.debug("Database manager initialized")
            
            # Initialize session management
            self.session_manager = SessionManager(self.config, self.db_manager)
            await self.session_manager.initialize()
            self.logger.debug("Session manager initialized")

            # Initialize security components
            self.security_validator = SecurityValidator(self.config)
            self.approval_manager = ApprovalManager(self.config)
            self.logger.debug("Security components initialized")
            
            # Initialize AI provider management
            self.provider_manager = ProviderManager(self.config)
            await self.provider_manager.initialize()
            self.logger.debug("Provider manager initialized")
            
            # Initialize tool system
            self.tool_manager = ToolManager(self.config)
            await self.tool_manager.initialize()
            self.logger.debug("Tool manager initialized")
            
            # Initialize agent engine
            self.agent_engine = AgentEngine(
                config=self.config,
                provider_manager=self.provider_manager,
                tool_manager=self.tool_manager,
                session_manager=self.session_manager,
                security_validator=self.security_validator,
                approval_manager=self.approval_manager,
            )
            await self.agent_engine.initialize()
            self.logger.debug("Agent engine initialized")
            
            # Initialize terminal UI
            self.terminal_app = TerminalApp(
                config=self.config,
                agent_engine=self.agent_engine,
                session_manager=self.session_manager,
                tool_manager=self.tool_manager,
                approval_manager=self.approval_manager,
            )
            self.logger.debug("Terminal UI initialized")
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize application: {e}")
            raise
    
    async def run(self) -> None:
        """Run the main application."""
        try:
            # Setup signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            # Initialize all components
            await self.initialize()
            
            self.running = True
            self.logger.info("Starting AI Terminal application")
            
            # Load or create default session
            default_session = self.config.get("session.default_session", "main")
            await self.session_manager.load_session(default_session)

            # Load user's work directory context
            await self._load_work_directory_context()

            # Start the terminal UI
            await self.terminal_app.run()
            
        except KeyboardInterrupt:
            self.logger.info("Application interrupted by user")
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            raise
        finally:
            await self.shutdown()

    async def _load_work_directory_context(self) -> None:
        """Automatically load full context of user's work directory and files."""
        try:
            import os
            from pathlib import Path

            work_dir = Path.cwd()
            self.logger.info(f"Loading work directory context: {work_dir}")

            # Get directory structure
            context_info = {
                "work_directory": str(work_dir),
                "files": [],
                "directories": [],
                "git_info": None,
                "project_info": None
            }

            # Scan directory structure (limit depth to avoid huge directories)
            for root, dirs, files in os.walk(work_dir):
                # Skip hidden directories and common build/cache directories
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in {
                    'node_modules', '__pycache__', '.git', 'venv', 'env',
                    'build', 'dist', 'target', '.vscode', '.idea'
                }]

                # Limit depth
                level = root[len(str(work_dir)):].count(os.sep)
                if level >= 3:
                    dirs[:] = []
                    continue

                rel_root = os.path.relpath(root, work_dir)
                if rel_root != '.':
                    context_info["directories"].append(rel_root)

                for file in files:
                    if not file.startswith('.') and self._is_text_file(file):
                        rel_path = os.path.join(rel_root, file) if rel_root != '.' else file
                        context_info["files"].append(rel_path)

            # Check for git repository
            if (work_dir / '.git').exists():
                try:
                    import subprocess
                    result = subprocess.run(
                        ['git', 'status', '--porcelain'],
                        capture_output=True,
                        text=True,
                        cwd=work_dir,
                        timeout=5
                    )
                    if result.returncode == 0:
                        context_info["git_info"] = {
                            "is_git_repo": True,
                            "status": result.stdout.strip()
                        }
                except Exception:
                    pass

            # Check for project files
            project_files = {
                'package.json': 'Node.js',
                'requirements.txt': 'Python',
                'pyproject.toml': 'Python',
                'Cargo.toml': 'Rust',
                'go.mod': 'Go',
                'pom.xml': 'Java',
                'composer.json': 'PHP'
            }

            for file, project_type in project_files.items():
                if (work_dir / file).exists():
                    context_info["project_info"] = {
                        "type": project_type,
                        "config_file": file
                    }
                    break

            # Store context for agent
            self.work_context = context_info

            # Add to agent's system prompt
            if self.agent_engine and hasattr(self.agent_engine, 'system_prompt') and context_info:
                context_prompt = self._build_context_prompt(context_info)
                self.agent_engine.system_prompt += f"\n\n{context_prompt}"

            if context_info:
                self.logger.info(f"Loaded context: {len(context_info['files'])} files, {len(context_info['directories'])} directories")

        except Exception as e:
            self.logger.error(f"Failed to load work directory context: {e}")

    def _is_text_file(self, filename: str) -> bool:
        """Check if file is likely a text file."""
        from pathlib import Path

        text_extensions = {
            '.py', '.js', '.ts', '.jsx', '.tsx', '.html', '.css', '.scss', '.sass',
            '.json', '.xml', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf',
            '.md', '.txt', '.rst', '.tex', '.sql', '.sh', '.bash', '.zsh',
            '.c', '.cpp', '.h', '.hpp', '.java', '.kt', '.swift', '.go', '.rs',
            '.php', '.rb', '.pl', '.r', '.m', '.scala', '.clj', '.hs', '.elm',
            '.vue', '.svelte', '.dockerfile', '.gitignore', '.env'
        }

        ext = Path(filename).suffix.lower()
        return ext in text_extensions or filename.lower() in {
            'readme', 'license', 'changelog', 'makefile', 'dockerfile'
        }

    def _build_context_prompt(self, context_info: dict) -> str:
        """Build context prompt for the agent."""
        prompt_parts = [
            "## Current Work Directory Context",
            f"Working in: {context_info['work_directory']}",
        ]

        if context_info.get('project_info'):
            proj = context_info['project_info']
            prompt_parts.append(f"Project Type: {proj['type']} (detected from {proj['config_file']})")

        if context_info.get('git_info', {}).get('is_git_repo'):
            prompt_parts.append("Git repository detected")

        if context_info['files']:
            prompt_parts.append(f"Available files ({len(context_info['files'])} total):")
            # Show first 20 files
            for file in context_info['files'][:20]:
                prompt_parts.append(f"  - {file}")
            if len(context_info['files']) > 20:
                prompt_parts.append(f"  ... and {len(context_info['files']) - 20} more files")

        if context_info['directories']:
            prompt_parts.append(f"Directory structure:")
            for directory in context_info['directories'][:10]:
                prompt_parts.append(f"  - {directory}/")
            if len(context_info['directories']) > 10:
                prompt_parts.append(f"  ... and {len(context_info['directories']) - 10} more directories")

        prompt_parts.append(
            "\nYou have full access to read, write, and modify files in this directory. "
            "Use the file operations tools to help the user with their project."
        )

        return "\n".join(prompt_parts)

    async def shutdown(self) -> None:
        """Gracefully shutdown the application."""
        if not self.running:
            return
        
        self.logger.info("Shutting down AI Terminal application...")
        self.running = False
        
        try:
            # Shutdown components in reverse order
            if self.terminal_app:
                await self.terminal_app.shutdown()
                self.logger.debug("Terminal UI shutdown complete")
            
            if self.agent_engine:
                await self.agent_engine.shutdown()
                self.logger.debug("Agent engine shutdown complete")
            
            if self.tool_manager:
                await self.tool_manager.shutdown()
                self.logger.debug("Tool manager shutdown complete")
            
            if self.provider_manager:
                await self.provider_manager.shutdown()
                self.logger.debug("Provider manager shutdown complete")
            
            if self.session_manager:
                await self.session_manager.shutdown()
                self.logger.debug("Session manager shutdown complete")
            
            if self.db_manager:
                await self.db_manager.shutdown()
                self.logger.debug("Database manager shutdown complete")
            
            self.logger.info("Application shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        if sys.platform != "win32":
            # Unix-like systems
            loop = asyncio.get_event_loop()
            
            def signal_handler(signum, frame):
                self.logger.info(f"Received signal {signum}, initiating shutdown...")
                loop.create_task(self.shutdown())
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
        else:
            # Windows
            def signal_handler(signum, frame):
                self.logger.info(f"Received signal {signum}, initiating shutdown...")
                asyncio.create_task(self.shutdown())
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
    
    async def restart(self) -> None:
        """Restart the application."""
        self.logger.info("Restarting AI Terminal application...")
        await self.shutdown()
        await self.run()
    
    def get_status(self) -> dict:
        """Get application status information."""
        return {
            "running": self.running,
            "components": {
                "database": self.db_manager is not None,
                "sessions": self.session_manager is not None,
                "providers": self.provider_manager is not None,
                "tools": self.tool_manager is not None,
                "agent": self.agent_engine is not None,
                "terminal": self.terminal_app is not None,
                "security": self.security_validator is not None,
                "approval": self.approval_manager is not None,
            },
            "config": {
                "providers": len(self.config.get("ai.providers", {})),
                "default_provider": self.config.get("ai.default_provider"),
                "default_model": self.config.get("ai.default_model"),
            },
        }
