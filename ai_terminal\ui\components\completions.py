"""
Auto-completion overlay component for AI Terminal.

Provides intelligent command and parameter suggestions with real-time filtering
and keyboard navigation for enhanced user experience.
"""

from typing import Dict, List, Optional

from rich.text import Text
from textual import on
from textual.containers import Vertical
from textual.events import Key
from textual.message import Message
from textual.widgets import ListI<PERSON>, ListView, Static

from ai_terminal.core.logger import LoggerMixin


class CompletionItem(ListItem):
    """Individual completion item."""
    
    def __init__(self, completion: str, description: str = "", **kwargs):
        """Initialize completion item."""
        super().__init__(**kwargs)
        self.completion = completion
        self.description = description
        
        # Create display text
        display_text = Text()
        display_text.append(completion, style="cyan bold")
        if description:
            display_text.append(f" - {description}", style="dim white")
        
        self.update(display_text)


class CompletionsOverlay(Vertical, LoggerMixin):
    """
    Auto-completion overlay component.
    
    Shows intelligent suggestions for commands, parameters, and file paths
    with keyboard navigation and real-time filtering.
    """
    
    DEFAULT_CSS = """
    CompletionsOverlay {
        width: 60%;
        height: auto;
        max-height: 10;
        background: $panel;
        border: solid $accent;
        border-title-align: left;
        margin: 0;
        padding: 0;
    }
    
    .completions-list {
        width: 100%;
        height: auto;
        max-height: 8;
        padding: 0;
    }
    
    .completion-item {
        height: 1;
        padding: 0 1;
    }
    
    .completion-item:hover {
        background: $accent;
    }
    
    .completion-selected {
        background: $primary;
        color: $text;
    }
    """
    
    def __init__(self, completions: List[str], **kwargs):
        """Initialize completions overlay."""
        super().__init__(**kwargs)
        
        self.completions = completions
        self.filtered_completions = completions.copy()
        self.selected_index = 0
        
        # Components
        self.completions_list: ListView = None
        
        self.border_title = f"💡 {len(completions)} suggestions"
        self.add_class("completions-overlay")
    
    def compose(self):
        """Compose completions overlay."""
        self.completions_list = ListView(classes="completions-list")
        yield self.completions_list
    
    def on_mount(self) -> None:
        """Handle component mount."""
        self._populate_completions()
    
    def _populate_completions(self) -> None:
        """Populate the completions list."""
        if not self.completions_list:
            return
        
        # Clear existing items
        self.completions_list.clear()
        
        # Add completion items
        for i, completion in enumerate(self.filtered_completions):
            description = self._get_completion_description(completion)
            item = CompletionItem(completion, description)
            
            if i == self.selected_index:
                item.add_class("completion-selected")
            
            self.completions_list.append(item)
        
        # Update title
        self.border_title = f"💡 {len(self.filtered_completions)} suggestions"
    
    def _get_completion_description(self, completion: str) -> str:
        """Get description for a completion."""
        # Slash command descriptions
        descriptions = {
            "/clear": "Clear current conversation",
            "/compact": "Compress conversation history",
            "/history": "Browse conversation history",
            "/sessions": "Manage chat sessions",
            "/model": "Switch between AI models",
            "/diff": "Show file differences",
            "/help": "Show help information",
            "/config": "Configuration management",
            "/export": "Export conversations",
            "/import": "Import conversations",
            "/quit": "Exit the application",
            "/restart": "Restart the application",
            "/status": "Show system status",
            "/tools": "List available tools",
        }
        
        return descriptions.get(completion, "")
    
    def filter_completions(self, filter_text: str) -> None:
        """Filter completions based on input text."""
        if not filter_text:
            self.filtered_completions = self.completions.copy()
        else:
            self.filtered_completions = [
                comp for comp in self.completions
                if comp.lower().startswith(filter_text.lower())
            ]
        
        self.selected_index = 0
        self._populate_completions()
    
    @on(Key)
    async def on_key(self, event: Key) -> None:
        """Handle key events."""
        if event.key == "up":
            self._move_selection(-1)
            event.prevent_default()
        elif event.key == "down":
            self._move_selection(1)
            event.prevent_default()
        elif event.key == "enter":
            await self._select_completion()
            event.prevent_default()
        elif event.key == "escape":
            await self._close()
            event.prevent_default()
    
    def _move_selection(self, direction: int) -> None:
        """Move selection up or down."""
        if not self.filtered_completions:
            return
        
        old_index = self.selected_index
        self.selected_index = max(0, min(
            len(self.filtered_completions) - 1,
            self.selected_index + direction
        ))
        
        if old_index != self.selected_index:
            self._update_selection()
    
    def _update_selection(self) -> None:
        """Update visual selection."""
        if not self.completions_list:
            return
        
        # Remove old selection
        for item in self.completions_list.children:
            item.remove_class("completion-selected")
        
        # Add new selection
        if 0 <= self.selected_index < len(self.completions_list.children):
            selected_item = list(self.completions_list.children)[self.selected_index]
            selected_item.add_class("completion-selected")
    
    async def _select_completion(self) -> None:
        """Select the current completion."""
        if (0 <= self.selected_index < len(self.filtered_completions)):
            selected = self.filtered_completions[self.selected_index]
            # TODO: Send selected completion to input component
            self.logger.debug(f"Selected completion: {selected}")
        
        await self._close()
    
    async def _close(self) -> None:
        """Close the completions overlay."""
        if self.parent:
            await self.remove()
    
    def get_selected_completion(self) -> str:
        """Get the currently selected completion."""
        if 0 <= self.selected_index < len(self.filtered_completions):
            return self.filtered_completions[self.selected_index]
        return ""


class TypeaheadOverlay(Vertical, LoggerMixin):
    """
    Advanced typeahead overlay with intelligent suggestions.

    Provides context-aware suggestions for commands, file paths,
    function names, and other contextual completions.
    """

    DEFAULT_CSS = """
    TypeaheadOverlay {
        width: 80%;
        height: auto;
        max-height: 15;
        background: $panel;
        border: solid $primary;
        border-title-align: left;
        margin: 0;
        padding: 0;
    }

    .typeahead-header {
        height: 1;
        background: $primary;
        color: $text;
        padding: 0 1;
    }

    .typeahead-list {
        width: 100%;
        height: auto;
        max-height: 12;
        padding: 0;
    }

    .typeahead-item {
        height: 2;
        padding: 0 1;
        border-bottom: solid $accent;
    }

    .typeahead-item:hover {
        background: $accent;
    }

    .typeahead-selected {
        background: $primary;
        color: $text;
    }

    .suggestion-main {
        color: $text;
        style: bold;
    }

    .suggestion-description {
        color: $text-muted;
        style: dim;
    }

    .suggestion-type {
        color: $accent;
        style: italic;
    }
    """

    def __init__(self, context: str = "", **kwargs):
        """Initialize typeahead overlay."""
        super().__init__(**kwargs)

        self.context = context
        self.suggestions: List[Dict[str, str]] = []
        self.filtered_suggestions: List[Dict[str, str]] = []
        self.selected_index = 0
        self.query = ""

        # Components
        self.typeahead_list: ListView = None

        self.border_title = "💡 Smart Suggestions"
        self.add_class("typeahead-overlay")

    def compose(self):
        """Compose typeahead overlay."""
        # Header
        yield Static("Smart Suggestions", classes="typeahead-header")

        # Suggestions list
        self.typeahead_list = ListView(classes="typeahead-list")
        yield self.typeahead_list

    def on_mount(self) -> None:
        """Handle component mount."""
        self._generate_suggestions()
        self._populate_suggestions()

    def _generate_suggestions(self) -> None:
        """Generate context-aware suggestions."""
        self.suggestions = []

        # Command suggestions
        if self.context.startswith("/") or not self.context:
            self.suggestions.extend([
                {
                    "text": "/clear",
                    "description": "Clear current conversation",
                    "type": "command"
                },
                {
                    "text": "/compact",
                    "description": "Compress conversation history",
                    "type": "command"
                },
                {
                    "text": "/history",
                    "description": "Browse conversation history",
                    "type": "command"
                },
                {
                    "text": "/sessions",
                    "description": "Manage chat sessions",
                    "type": "command"
                },
                {
                    "text": "/model",
                    "description": "Switch between AI models",
                    "type": "command"
                },
                {
                    "text": "/diff",
                    "description": "Show file differences",
                    "type": "command"
                },
                {
                    "text": "/help",
                    "description": "Show help information",
                    "type": "command"
                },
                {
                    "text": "/config",
                    "description": "Configuration management",
                    "type": "command"
                },
                {
                    "text": "/export",
                    "description": "Export conversations",
                    "type": "command"
                },
                {
                    "text": "/import",
                    "description": "Import conversations",
                    "type": "command"
                },
            ])

        # File path suggestions
        if "/" in self.context or "\\" in self.context:
            self.suggestions.extend([
                {
                    "text": "./",
                    "description": "Current directory",
                    "type": "path"
                },
                {
                    "text": "../",
                    "description": "Parent directory",
                    "type": "path"
                },
                {
                    "text": "~/",
                    "description": "Home directory",
                    "type": "path"
                },
            ])

        self.filtered_suggestions = self.suggestions.copy()

    def filter_suggestions(self, query: str) -> None:
        """Filter suggestions based on query."""
        self.query = query.lower()

        if not self.query:
            self.filtered_suggestions = self.suggestions.copy()
        else:
            self.filtered_suggestions = [
                suggestion for suggestion in self.suggestions
                if (self.query in suggestion["text"].lower() or
                    self.query in suggestion["description"].lower())
            ]

        self.selected_index = 0
        self._populate_suggestions()

    def _populate_suggestions(self) -> None:
        """Populate the suggestions list."""
        if not self.typeahead_list:
            return

        self.typeahead_list.clear()

        for i, suggestion in enumerate(self.filtered_suggestions):
            item_content = self._create_suggestion_item(suggestion, i == self.selected_index)
            list_item = ListItem(item_content, classes="typeahead-item")

            if i == self.selected_index:
                list_item.add_class("typeahead-selected")

            self.typeahead_list.append(list_item)

        # Update border title
        self.border_title = f"💡 {len(self.filtered_suggestions)} suggestions"

    def _create_suggestion_item(self, suggestion: Dict[str, str], is_selected: bool) -> Static:
        """Create a suggestion item widget."""
        content = Text()

        # Type indicator
        type_icons = {
            "command": "⚡",
            "path": "📁",
            "code": "💻",
            "git": "🔀",
            "file": "📄",
        }
        icon = type_icons.get(suggestion["type"], "💡")
        content.append(f"{icon} ", style="suggestion-type")

        # Main text
        content.append(suggestion["text"], style="suggestion-main")
        content.append("\n")

        # Description
        content.append(f"  {suggestion['description']}", style="suggestion-description")

        return Static(content)

    def move_selection(self, direction: int) -> None:
        """Move selection up or down."""
        if not self.filtered_suggestions:
            return

        old_index = self.selected_index
        self.selected_index = max(0, min(
            len(self.filtered_suggestions) - 1,
            self.selected_index + direction
        ))

        if old_index != self.selected_index:
            self._update_selection()

    def _update_selection(self) -> None:
        """Update visual selection."""
        if not self.typeahead_list:
            return

        # Remove old selection
        for item in self.typeahead_list.children:
            item.remove_class("typeahead-selected")

        # Add new selection
        if 0 <= self.selected_index < len(self.typeahead_list.children):
            self.typeahead_list.children[self.selected_index].add_class("typeahead-selected")

    def get_selected_suggestion(self) -> Optional[Dict[str, str]]:
        """Get the currently selected suggestion."""
        if 0 <= self.selected_index < len(self.filtered_suggestions):
            return self.filtered_suggestions[self.selected_index]
        return None

    async def on_key(self, event: Key) -> None:
        """Handle key events."""
        if event.key == "up":
            self.move_selection(-1)
            event.prevent_default()
        elif event.key == "down":
            self.move_selection(1)
            event.prevent_default()
        elif event.key == "enter":
            selected = self.get_selected_suggestion()
            if selected:
                # Emit selection event
                self.post_message(self.SuggestionSelected(selected))
            event.prevent_default()
        elif event.key == "escape":
            await self.remove()
            event.prevent_default()

    class SuggestionSelected(Message):
        """Message sent when a suggestion is selected."""

        def __init__(self, suggestion: Dict[str, str]) -> None:
            self.suggestion = suggestion
            super().__init__()
