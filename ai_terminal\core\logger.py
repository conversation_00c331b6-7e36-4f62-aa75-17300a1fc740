"""
Logging configuration for AI Terminal.

Provides structured logging with multiple handlers, log rotation,
and configurable verbosity levels for debugging and monitoring.
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>


def setup_logging(
    level: str = "INFO",
    log_dir: Optional[Path] = None,
    enable_file_logging: bool = True,
    enable_console_logging: bool = True,
) -> None:
    """
    Setup comprehensive logging for the application.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory for log files (default: ~/.config/ai-terminal/logs)
        enable_file_logging: Whether to enable file logging
        enable_console_logging: Whether to enable console logging
    """
    # Convert string level to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Setup console logging with Rich
    if enable_console_logging:
        console = Console(stderr=True)
        console_handler = RichHandler(
            console=console,
            show_time=True,
            show_path=True,
            markup=True,
            rich_tracebacks=True,
            tracebacks_show_locals=numeric_level <= logging.DEBUG,
        )
        console_handler.setLevel(numeric_level)
        
        console_formatter = logging.Formatter(
            fmt="%(message)s",
            datefmt="[%X]",
        )
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
    
    # Setup file logging
    if enable_file_logging:
        if log_dir is None:
            # Default log directory
            if sys.platform == "win32":
                log_dir = Path.home() / "AppData" / "Local" / "ai-terminal" / "logs"
            else:
                log_dir = Path.home() / ".config" / "ai-terminal" / "logs"
        
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Main application log with rotation
        app_log_file = log_dir / "ai-terminal.log"
        file_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding="utf-8",
        )
        file_handler.setLevel(numeric_level)
        
        file_formatter = logging.Formatter(
            fmt="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        # Error log for warnings and above
        error_log_file = log_dir / "errors.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=5 * 1024 * 1024,  # 5MB
            backupCount=3,
            encoding="utf-8",
        )
        error_handler.setLevel(logging.WARNING)
        error_handler.setFormatter(file_formatter)
        root_logger.addHandler(error_handler)
        
        # Debug log for detailed debugging (only when debug level)
        if numeric_level <= logging.DEBUG:
            debug_log_file = log_dir / "debug.log"
            debug_handler = logging.handlers.RotatingFileHandler(
                debug_log_file,
                maxBytes=20 * 1024 * 1024,  # 20MB
                backupCount=2,
                encoding="utf-8",
            )
            debug_handler.setLevel(logging.DEBUG)
            
            debug_formatter = logging.Formatter(
                fmt="%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s",
                datefmt="%Y-%m-%d %H:%M:%S",
            )
            debug_handler.setFormatter(debug_formatter)
            root_logger.addHandler(debug_handler)
    
    # Set specific logger levels for noisy libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("textual").setLevel(logging.WARNING)
    
    # Create application-specific loggers
    app_logger = logging.getLogger("ai_terminal")
    app_logger.setLevel(numeric_level)
    
    agent_logger = logging.getLogger("ai_terminal.agent")
    agent_logger.setLevel(numeric_level)
    
    tools_logger = logging.getLogger("ai_terminal.tools")
    tools_logger.setLevel(numeric_level)
    
    security_logger = logging.getLogger("ai_terminal.security")
    security_logger.setLevel(numeric_level)


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance for the given name."""
    return logging.getLogger(f"ai_terminal.{name}")


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger instance for this class."""
        return get_logger(self.__class__.__name__.lower())
