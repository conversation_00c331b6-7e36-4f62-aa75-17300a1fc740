"""
Session manager for AI Terminal.

Handles conversation session management, message storage, and retrieval
with support for session switching and conversation history.
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

from sqlalchemy import desc, func
from sqlalchemy.orm import selectinload

from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin
from ai_terminal.storage.database import DatabaseManager
from ai_terminal.storage.models import Message, Session


class SessionManager(LoggerMixin):
    """
    Session manager for conversation handling.
    
    Manages conversation sessions, message storage, and retrieval
    with support for session switching and history management.
    """
    
    def __init__(self, config: Config, db_manager: DatabaseManager):
        """Initialize session manager."""
        self.config = config
        self.db_manager = db_manager
        self.current_session: Optional[Session] = None
        self.auto_save = config.get("session.auto_save", True)
        self.max_history = config.get("session.max_history", 10000)
    
    async def initialize(self) -> None:
        """Initialize the session manager."""
        try:
            # Load or create default session
            default_session_name = self.config.get("session.default_session", "main")
            await self.load_session(default_session_name)
            
            self.logger.info("Session manager initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize session manager: {e}")
            raise
    
    async def create_session(
        self, 
        name: str, 
        description: Optional[str] = None,
        model: Optional[str] = None,
        provider: Optional[str] = None
    ) -> Session:
        """Create a new conversation session."""
        try:
            # Check if session with name already exists
            existing = await self.get_session_by_name(name)
            if existing:
                raise ValueError(f"Session '{name}' already exists")
            
            # Create new session
            session = Session(
                id=str(uuid.uuid4()),
                name=name,
                description=description,
                model=model or self.config.get("ai.default_model"),
                provider=provider or self.config.get("ai.default_provider"),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
                last_accessed=datetime.utcnow(),
            )
            
            # Save to database
            with self.db_manager.get_session() as db_session:
                db_session.add(session)
                db_session.commit()
                db_session.refresh(session)
            
            self.logger.info(f"Created session: {name}")
            return session
            
        except Exception as e:
            self.logger.error(f"Failed to create session: {e}")
            raise
    
    async def load_session(self, session_name: str) -> Session:
        """Load a session by name."""
        try:
            # Try to get existing session
            session = await self.get_session_by_name(session_name)
            
            if not session:
                # Create new session if it doesn't exist
                session = await self.create_session(session_name)
            
            # Update last accessed time
            session.last_accessed = datetime.utcnow()
            with self.db_manager.get_session() as db_session:
                db_session.merge(session)
                db_session.commit()
            
            self.current_session = session
            self.logger.info(f"Loaded session: {session_name}")
            return session
            
        except Exception as e:
            self.logger.error(f"Failed to load session: {e}")
            raise
    
    async def get_session(self, session_id: str) -> Optional[Session]:
        """Get a session by ID."""
        try:
            with self.db_manager.get_session() as db_session:
                session = db_session.query(Session).filter(
                    Session.id == session_id,
                    Session.is_deleted == False
                ).first()
                return session
                
        except Exception as e:
            self.logger.error(f"Failed to get session: {e}")
            return None
    
    async def get_session_by_name(self, name: str) -> Optional[Session]:
        """Get a session by name."""
        try:
            with self.db_manager.get_session() as db_session:
                session = db_session.query(Session).filter(
                    Session.name == name,
                    Session.is_active == True
                ).first()
                return session
                
        except Exception as e:
            self.logger.error(f"Failed to get session by name: {e}")
            return None
    
    async def list_sessions(self, limit: int = 100) -> List[Session]:
        """List all sessions."""
        try:
            with self.db_manager.get_session() as db_session:
                sessions = db_session.query(Session).filter(
                    Session.is_active == True
                ).order_by(desc(Session.last_accessed)).limit(limit).all()
                return sessions
                
        except Exception as e:
            self.logger.error(f"Failed to list sessions: {e}")
            return []
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a session."""
        try:
            with self.db_manager.get_session() as db_session:
                session = db_session.query(Session).filter(
                    Session.id == session_id
                ).first()
                
                if not session:
                    return False
                
                # Soft delete
                session.is_active = False
                session.is_archived = True
                session.updated_at = datetime.utcnow()
                
                db_session.commit()
                
                # If this was the current session, switch to default
                if self.current_session and self.current_session.id == session_id:
                    await self.load_session("main")
                
                self.logger.info(f"Deleted session: {session.name}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to delete session: {e}")
            return False
    
    async def add_message(
        self,
        session_id: str,
        role: str,
        content: str,
        model: Optional[str] = None,
        provider: Optional[str] = None,
        tool_calls: Optional[List[dict]] = None,
        metadata: Optional[dict] = None
    ) -> Message:
        """Add a message to a session."""
        try:
            message = Message(
                id=str(uuid.uuid4()),
                session_id=session_id,
                role=role,
                content=content,
                model=model,
                provider=provider,
                tool_calls=tool_calls,
                metadata=metadata or {},
                timestamp=datetime.utcnow(),
            )
            
            with self.db_manager.get_session() as db_session:
                db_session.add(message)
                
                # Update session statistics
                session = db_session.query(Session).filter(
                    Session.id == session_id
                ).first()
                
                if session:
                    session.message_count += 1
                    session.updated_at = datetime.utcnow()
                    session.last_accessed = datetime.utcnow()
                
                db_session.commit()
                db_session.refresh(message)
            
            # Auto-cleanup old messages if needed
            if self.auto_save:
                await self._cleanup_old_messages(session_id)
            
            return message
            
        except Exception as e:
            self.logger.error(f"Failed to add message: {e}")
            raise
    
    async def get_session_messages(
        self, 
        session_id: str, 
        limit: Optional[int] = None,
        offset: int = 0
    ) -> List[Message]:
        """Get messages for a session."""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(Message).filter(
                    Message.session_id == session_id,
                    Message.is_deleted == False
                ).order_by(Message.timestamp)
                
                if offset > 0:
                    query = query.offset(offset)
                
                if limit:
                    query = query.limit(limit)
                
                messages = query.all()
                return messages
                
        except Exception as e:
            self.logger.error(f"Failed to get session messages: {e}")
            return []
    
    async def search_messages(
        self, 
        query: str, 
        session_id: Optional[str] = None,
        limit: int = 50
    ) -> List[Message]:
        """Search messages by content."""
        try:
            with self.db_manager.get_session() as db_session:
                db_query = db_session.query(Message).filter(
                    Message.content.contains(query),
                    Message.is_deleted == False
                )
                
                if session_id:
                    db_query = db_query.filter(Message.session_id == session_id)
                
                messages = db_query.order_by(desc(Message.timestamp)).limit(limit).all()
                return messages
                
        except Exception as e:
            self.logger.error(f"Failed to search messages: {e}")
            return []
    
    async def get_current_session(self) -> Optional[Session]:
        """Get the current active session."""
        return self.current_session
    
    async def save_current_session(self) -> None:
        """Save the current session state."""
        if not self.current_session:
            return
        
        try:
            with self.db_manager.get_session() as db_session:
                self.current_session.updated_at = datetime.utcnow()
                db_session.merge(self.current_session)
                db_session.commit()
            
            self.logger.debug("Current session saved")
            
        except Exception as e:
            self.logger.error(f"Failed to save current session: {e}")
    
    async def _cleanup_old_messages(self, session_id: str) -> None:
        """Clean up old messages if session exceeds max history."""
        try:
            with self.db_manager.get_session() as db_session:
                # Count messages in session
                count = db_session.query(func.count(Message.id)).filter(
                    Message.session_id == session_id,
                    Message.is_deleted == False
                ).scalar()
                
                if count > self.max_history:
                    # Get oldest messages to delete
                    excess_count = count - self.max_history
                    old_messages = db_session.query(Message).filter(
                        Message.session_id == session_id,
                        Message.is_deleted == False
                    ).order_by(Message.timestamp).limit(excess_count).all()
                    
                    # Soft delete old messages
                    for message in old_messages:
                        message.is_deleted = True
                    
                    db_session.commit()
                    
                    self.logger.debug(f"Cleaned up {excess_count} old messages")
                    
        except Exception as e:
            self.logger.error(f"Failed to cleanup old messages: {e}")
    
    async def export_session(self, session_id: str) -> dict:
        """Export a session to dictionary format."""
        try:
            session = await self.get_session(session_id)
            if not session:
                raise ValueError(f"Session {session_id} not found")
            
            messages = await self.get_session_messages(session_id)
            
            return {
                "session": session.to_dict(),
                "messages": [msg.to_dict() for msg in messages]
            }
            
        except Exception as e:
            self.logger.error(f"Failed to export session: {e}")
            raise
    
    async def import_session(self, session_data: dict) -> Session:
        """Import a session from dictionary format."""
        try:
            # Create session
            session_dict = session_data["session"]
            session = Session.from_dict(session_dict)
            
            # Generate new ID to avoid conflicts
            session.id = str(uuid.uuid4())
            
            with self.db_manager.get_session() as db_session:
                db_session.add(session)
                db_session.commit()
                db_session.refresh(session)
                
                # Import messages
                for msg_dict in session_data.get("messages", []):
                    message = Message.from_dict(msg_dict)
                    message.id = str(uuid.uuid4())
                    message.session_id = session.id
                    db_session.add(message)
                
                db_session.commit()
            
            self.logger.info(f"Imported session: {session.name}")
            return session
            
        except Exception as e:
            self.logger.error(f"Failed to import session: {e}")
            raise
    
    async def shutdown(self) -> None:
        """Shutdown the session manager."""
        try:
            if self.auto_save and self.current_session:
                await self.save_current_session()
            
            self.logger.info("Session manager shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during session manager shutdown: {e}")

    async def get_session_by_name(self, name: str) -> Optional[Session]:
        """Get session by name."""
        try:
            sessions = await self.list_sessions()
            for session in sessions:
                if session.name == name:
                    return session
            return None
        except Exception as e:
            self.logger.error(f"Failed to get session by name: {e}")
            return None

    async def export_session(self, session_id: str) -> Dict[str, Any]:
        """Export session data for backup/sharing."""
        try:
            session = await self.get_session(session_id)
            if not session:
                raise ValueError(f"Session {session_id} not found")

            messages = await self.get_messages(session_id)

            export_data = {
                "session": {
                    "id": session.id,
                    "name": session.name,
                    "description": session.description,
                    "created_at": session.created_at.isoformat(),
                    "last_accessed": session.last_accessed.isoformat(),
                    "provider": session.provider,
                    "model": session.model,
                    "system_prompt": session.system_prompt,
                    "context_length": session.context_length,
                    "temperature": session.temperature,
                    "metadata": session.session_metadata
                },
                "messages": [
                    {
                        "id": msg.id,
                        "role": msg.role,
                        "content": msg.content,
                        "timestamp": msg.timestamp.isoformat(),
                        "tool_calls": msg.tool_calls,
                        "metadata": msg.message_metadata
                    }
                    for msg in messages
                ],
                "export_timestamp": datetime.now().isoformat(),
                "version": "1.0"
            }

            return export_data

        except Exception as e:
            self.logger.error(f"Failed to export session: {e}")
            raise

    async def import_session(self, import_data: Dict[str, Any]) -> Session:
        """Import session data from backup/sharing."""
        try:
            session_data = import_data["session"]
            messages_data = import_data.get("messages", [])

            # Create new session
            session = Session(
                id=str(uuid.uuid4()),  # Generate new ID
                name=f"{session_data['name']}_imported",
                description=session_data.get("description", ""),
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                provider=session_data.get("provider", "deepseek"),
                model=session_data.get("model", "deepseek-chat"),
                system_prompt=session_data.get("system_prompt", ""),
                context_length=session_data.get("context_length", 8192),
                temperature=session_data.get("temperature", 0.7),
                session_metadata=session_data.get("metadata", {})
            )

            # Save session
            await self.database.save_session(session)

            # Import messages
            for msg_data in messages_data:
                message = Message(
                    id=str(uuid.uuid4()),  # Generate new ID
                    session_id=session.id,
                    role=msg_data["role"],
                    content=msg_data["content"],
                    timestamp=datetime.fromisoformat(msg_data["timestamp"]),
                    tool_calls=msg_data.get("tool_calls", []),
                    message_metadata=msg_data.get("metadata", {})
                )
                await self.database.save_message(message)

            self.logger.info(f"Imported session: {session.name}")
            return session

        except Exception as e:
            self.logger.error(f"Failed to import session: {e}")
            raise

    async def clear_session_messages(self, session_id: str) -> None:
        """Clear all messages from a session."""
        try:
            await self.database.clear_session_messages(session_id)
            self.logger.info(f"Cleared messages for session: {session_id}")
        except Exception as e:
            self.logger.error(f"Failed to clear session messages: {e}")
            raise

    async def get_session_messages(self, session_id: str) -> List[Message]:
        """Get all messages for a session (alias for get_messages)."""
        return await self.get_messages(session_id)
