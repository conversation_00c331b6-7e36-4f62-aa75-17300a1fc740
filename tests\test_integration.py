"""
Integration tests for AI Terminal.

Tests the complete system integration including slash commands,
enhanced tools, session management, and UI components.
"""

import asyncio
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from ai_terminal.core.app import AI<PERSON>er<PERSON>lApp
from ai_terminal.core.config import Config
from ai_terminal.storage.database import DatabaseManager
from ai_terminal.storage.session import SessionManager
from ai_terminal.ui.commands import SlashCommandProcessor


@pytest.fixture
async def temp_config():
    """Create a temporary configuration for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        config = Config(config_dir=Path(temp_dir))
        await config.initialize()
        yield config


@pytest.fixture
async def test_database(temp_config):
    """Create a test database."""
    db_manager = DatabaseManager(temp_config)
    await db_manager.initialize()
    yield db_manager
    await db_manager.shutdown()


@pytest.fixture
async def test_session_manager(temp_config, test_database):
    """Create a test session manager."""
    session_manager = SessionManager(temp_config, test_database)
    await session_manager.initialize()
    yield session_manager
    await session_manager.shutdown()


class TestSystemIntegration:
    """Test complete system integration."""
    
    @pytest.mark.asyncio
    async def test_session_lifecycle(self, test_session_manager):
        """Test complete session lifecycle."""
        # Create a new session
        session = await test_session_manager.create_session("test_session")
        assert session.name == "test_session"
        
        # Add messages to session
        from ai_terminal.storage.models import Message
        import uuid
        from datetime import datetime
        
        message1 = Message(
            id=str(uuid.uuid4()),
            session_id=session.id,
            role="user",
            content="Hello, AI!",
            timestamp=datetime.now()
        )
        
        message2 = Message(
            id=str(uuid.uuid4()),
            session_id=session.id,
            role="assistant",
            content="Hello! How can I help you today?",
            timestamp=datetime.now()
        )
        
        await test_session_manager.add_message(message1)
        await test_session_manager.add_message(message2)
        
        # Retrieve messages
        messages = await test_session_manager.get_messages(session.id)
        assert len(messages) == 2
        assert messages[0].content == "Hello, AI!"
        assert messages[1].content == "Hello! How can I help you today?"
        
        # Export session
        export_data = await test_session_manager.export_session(session.id)
        assert export_data["session"]["name"] == "test_session"
        assert len(export_data["messages"]) == 2
        
        # Import session
        imported_session = await test_session_manager.import_session(export_data)
        assert imported_session.name == "test_session_imported"
        
        imported_messages = await test_session_manager.get_messages(imported_session.id)
        assert len(imported_messages) == 2
        
        # Clear session messages
        await test_session_manager.clear_session_messages(session.id)
        cleared_messages = await test_session_manager.get_messages(session.id)
        assert len(cleared_messages) == 0
        
        # Delete session
        await test_session_manager.delete_session(session.id)
        deleted_session = await test_session_manager.get_session(session.id)
        assert deleted_session is None
    
    @pytest.mark.asyncio
    async def test_slash_command_integration(self, temp_config, test_session_manager):
        """Test slash command integration with session management."""
        # Create mock dependencies
        mock_agent_engine = AsyncMock()
        mock_agent_engine.current_provider = "test-provider"
        mock_agent_engine.current_model = "test-model"
        mock_agent_engine.is_processing = False
        
        mock_terminal_app = AsyncMock()
        mock_terminal_app.terminal_chat = AsyncMock()
        mock_terminal_app.terminal_chat.clear = AsyncMock()
        
        # Create slash command processor
        processor = SlashCommandProcessor(
            config=temp_config,
            session_manager=test_session_manager,
            agent_engine=mock_agent_engine,
            terminal_app=mock_terminal_app
        )
        
        # Test session creation via slash command
        result = await processor.execute_command("/sessions new integration_test")
        assert result["success"] is True
        
        # Verify session was created
        sessions = await test_session_manager.list_sessions()
        session_names = [s.name for s in sessions]
        assert "integration_test" in session_names
        
        # Test history command
        result = await processor.execute_command("/history")
        assert result["success"] is True
        assert "messages" in result["result"].lower()
        
        # Test status command
        result = await processor.execute_command("/status")
        assert result["success"] is True
        assert "AI Terminal Status" in result["result"]
        
        # Test configuration commands
        result = await processor.execute_command("/config ai.default_model new-model")
        assert result["success"] is True
        
        result = await processor.execute_command("/config ai.default_model")
        assert result["success"] is True
        assert "new-model" in result["result"]
    
    @pytest.mark.asyncio
    async def test_export_import_workflow(self, test_session_manager):
        """Test complete export/import workflow."""
        # Create session with messages
        session = await test_session_manager.create_session("export_test")
        
        from ai_terminal.storage.models import Message
        import uuid
        from datetime import datetime
        
        messages = [
            Message(
                id=str(uuid.uuid4()),
                session_id=session.id,
                role="user",
                content=f"Message {i}",
                timestamp=datetime.now()
            )
            for i in range(5)
        ]
        
        for msg in messages:
            await test_session_manager.add_message(msg)
        
        # Export session
        export_data = await test_session_manager.export_session(session.id)
        
        # Verify export structure
        assert "session" in export_data
        assert "messages" in export_data
        assert "export_timestamp" in export_data
        assert "version" in export_data
        
        assert export_data["session"]["name"] == "export_test"
        assert len(export_data["messages"]) == 5
        
        # Import into new session
        imported_session = await test_session_manager.import_session(export_data)
        
        # Verify imported data
        imported_messages = await test_session_manager.get_messages(imported_session.id)
        assert len(imported_messages) == 5
        
        for i, msg in enumerate(imported_messages):
            assert msg.content == f"Message {i}"
            assert msg.role == "user"
    
    @pytest.mark.asyncio
    @patch('subprocess.run')
    async def test_tool_integration(self, mock_run, temp_config):
        """Test tool integration with the system."""
        from ai_terminal.tools.git import GitTool
        from ai_terminal.tools.package_manager import PackageManagerTool
        
        # Mock successful git operations
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="* main\n  feature\n",
            stderr=""
        )
        
        git_tool = GitTool(temp_config)
        result = await git_tool._git_branch()
        
        assert result["success"] is True
        assert "main" in result["branches"]
        
        # Mock successful npm operations
        mock_run.return_value = MagicMock(
            returncode=0,
            stdout="+ package@1.0.0\n",
            stderr=""
        )
        
        package_tool = PackageManagerTool(temp_config)
        result = await package_tool._npm_operation("install", package="test-package")
        
        assert result["success"] is True
        assert result["package"] == "test-package"
    
    @pytest.mark.asyncio
    async def test_error_handling_integration(self, temp_config, test_session_manager):
        """Test error handling across the system."""
        # Test slash command with invalid session
        mock_agent_engine = AsyncMock()
        mock_terminal_app = AsyncMock()
        
        processor = SlashCommandProcessor(
            config=temp_config,
            session_manager=test_session_manager,
            agent_engine=mock_agent_engine,
            terminal_app=mock_terminal_app
        )
        
        # Test unknown command
        result = await processor.execute_command("/unknown_command")
        assert result["success"] is False
        assert "Unknown command" in result["error"]
        
        # Test command requiring confirmation
        result = await processor.execute_command("/clear")
        assert result["success"] is False
        assert result["requires_confirmation"] is True
        
        # Test session operations with invalid data
        try:
            await test_session_manager.get_session("nonexistent-id")
            session = None
        except:
            session = None
        
        assert session is None
    
    @pytest.mark.asyncio
    async def test_concurrent_operations(self, test_session_manager):
        """Test concurrent session operations."""
        # Create multiple sessions concurrently
        tasks = []
        for i in range(5):
            task = test_session_manager.create_session(f"concurrent_session_{i}")
            tasks.append(task)
        
        sessions = await asyncio.gather(*tasks)
        assert len(sessions) == 5
        
        # Add messages to sessions concurrently
        from ai_terminal.storage.models import Message
        import uuid
        from datetime import datetime
        
        message_tasks = []
        for session in sessions:
            message = Message(
                id=str(uuid.uuid4()),
                session_id=session.id,
                role="user",
                content=f"Message for {session.name}",
                timestamp=datetime.now()
            )
            task = test_session_manager.add_message(message)
            message_tasks.append(task)
        
        await asyncio.gather(*message_tasks)
        
        # Verify all messages were added
        for session in sessions:
            messages = await test_session_manager.get_messages(session.id)
            assert len(messages) == 1
            assert f"Message for {session.name}" in messages[0].content
    
    @pytest.mark.asyncio
    async def test_database_operations(self, test_database):
        """Test database operations directly."""
        # Test database stats
        stats = await test_database.get_database_stats()
        assert "session_count" in stats
        assert "message_count" in stats
        assert "schema_version" in stats
        
        # Test vacuum operation
        await test_database.vacuum_database()
        
        # Test backup and restore
        backup_path = await test_database.backup_database()
        assert backup_path.exists()
        
        # Clean up
        backup_path.unlink()
    
    @pytest.mark.asyncio
    async def test_configuration_persistence(self, temp_config):
        """Test configuration persistence."""
        # Set configuration values
        temp_config.set("test.value", "test_data")
        temp_config.set("test.number", 42)
        temp_config.set("test.boolean", True)
        
        # Save configuration
        await temp_config.save()
        
        # Create new config instance and load
        new_config = Config(config_dir=temp_config.config_dir)
        await new_config.initialize()
        
        # Verify values persisted
        assert new_config.get("test.value") == "test_data"
        assert new_config.get("test.number") == 42
        assert new_config.get("test.boolean") is True
