"""
Git operations tool for AI Terminal.

Provides comprehensive Git version control operations including
repository management, commits, branches, and remote operations.
"""

from typing import Any, Dict

from ai_terminal.tools.base import BaseTool, ToolExecutionError


class GitTool(BaseTool):
    """Git version control operations tool."""
    
    def __init__(self):
        """Initialize Git tool."""
        super().__init__(
            name="git",
            description="Git version control operations and repository management"
        )
        self.requires_approval = False
    
    async def execute(self, operation: str, **kwargs) -> Any:
        """Execute a Git operation."""
        # TODO: Implement Git operations
        operations = {
            "status": self._git_status,
            "add": self._git_add,
            "commit": self._git_commit,
            "push": self._git_push,
            "pull": self._git_pull,
            "branch": self._git_branch,
            "checkout": self._git_checkout,
            "log": self._git_log,
            "diff": self._git_diff,
            "clone": self._git_clone,
        }
        
        if operation not in operations:
            raise ToolExecutionError(f"Unknown Git operation: {operation}", self.name)
        
        return await operations[operation](**kwargs)
    
    async def _git_status(self, **kwargs) -> Dict[str, Any]:
        """Get Git repository status."""
        try:
            import subprocess
            import os

            # Check if we're in a git repository
            if not os.path.exists('.git') and not self._find_git_root():
                return {
                    "error": "Not a git repository",
                    "status": "not_a_repo",
                    "files": []
                }

            # Run git status
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                return {
                    "error": result.stderr.strip(),
                    "status": "error",
                    "files": []
                }

            # Parse status output
            files = []
            status_map = {
                'M': 'modified',
                'A': 'added',
                'D': 'deleted',
                'R': 'renamed',
                'C': 'copied',
                'U': 'unmerged',
                '?': 'untracked'
            }

            for line in result.stdout.strip().split('\n'):
                if line:
                    status_code = line[:2]
                    filename = line[3:]

                    staged = status_map.get(status_code[0], 'unknown') if status_code[0] != ' ' else None
                    unstaged = status_map.get(status_code[1], 'unknown') if status_code[1] != ' ' else None

                    files.append({
                        "file": filename,
                        "staged": staged,
                        "unstaged": unstaged
                    })

            # Get current branch
            branch_result = subprocess.run(
                ["git", "branch", "--show-current"],
                capture_output=True,
                text=True,
                timeout=5
            )
            current_branch = branch_result.stdout.strip() if branch_result.returncode == 0 else "unknown"

            return {
                "status": "clean" if not files else "dirty",
                "files": files,
                "current_branch": current_branch,
                "total_files": len(files)
            }

        except subprocess.TimeoutExpired:
            return {"error": "Git command timed out", "status": "timeout", "files": []}
        except FileNotFoundError:
            return {"error": "Git not found in PATH", "status": "no_git", "files": []}
        except Exception as e:
            self.logger.error(f"Git status failed: {e}")
            return {"error": str(e), "status": "error", "files": []}

    def _find_git_root(self) -> bool:
        """Find git root directory."""
        import os
        current = os.getcwd()
        while current != os.path.dirname(current):
            if os.path.exists(os.path.join(current, '.git')):
                return True
            current = os.path.dirname(current)
        return False
    
    async def _git_add(self, files: str = ".", **kwargs) -> Dict[str, Any]:
        """Add files to Git staging area."""
        try:
            import subprocess

            # Run git add
            result = subprocess.run(
                ["git", "add", files],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                return {
                    "error": result.stderr.strip(),
                    "success": False,
                    "files": files
                }

            # Get list of added files
            status_result = subprocess.run(
                ["git", "diff", "--cached", "--name-only"],
                capture_output=True,
                text=True,
                timeout=10
            )

            added_files = status_result.stdout.strip().split('\n') if status_result.stdout.strip() else []

            return {
                "success": True,
                "added_files": added_files,
                "files": files,
                "count": len(added_files)
            }

        except subprocess.TimeoutExpired:
            return {"error": "Git add command timed out", "success": False, "files": files}
        except Exception as e:
            self.logger.error(f"Git add failed: {e}")
            return {"error": str(e), "success": False, "files": files}

    async def _git_commit(self, message: str, **kwargs) -> Dict[str, Any]:
        """Commit changes to Git repository."""
        try:
            import subprocess

            if not message:
                return {"error": "Commit message is required", "success": False}

            # Run git commit
            result = subprocess.run(
                ["git", "commit", "-m", message],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                return {
                    "error": result.stderr.strip(),
                    "success": False,
                    "message": message
                }

            # Extract commit hash from output
            output = result.stdout.strip()
            commit_hash = ""
            if output:
                # Look for commit hash in output
                import re
                match = re.search(r'\[[\w\-]+\s+([a-f0-9]+)\]', output)
                if match:
                    commit_hash = match.group(1)

            return {
                "success": True,
                "commit_hash": commit_hash,
                "message": message,
                "output": output
            }

        except subprocess.TimeoutExpired:
            return {"error": "Git commit command timed out", "success": False, "message": message}
        except Exception as e:
            self.logger.error(f"Git commit failed: {e}")
            return {"error": str(e), "success": False, "message": message}

    async def _git_push(self, remote: str = "origin", branch: str = None, **kwargs) -> Dict[str, Any]:
        """Push changes to remote repository."""
        try:
            import subprocess

            # Get current branch if not specified
            if not branch:
                branch_result = subprocess.run(
                    ["git", "branch", "--show-current"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                branch = branch_result.stdout.strip() if branch_result.returncode == 0 else "main"

            # Run git push
            result = subprocess.run(
                ["git", "push", remote, branch],
                capture_output=True,
                text=True,
                timeout=60
            )

            if result.returncode != 0:
                return {
                    "error": result.stderr.strip(),
                    "success": False,
                    "remote": remote,
                    "branch": branch
                }

            return {
                "success": True,
                "remote": remote,
                "branch": branch,
                "output": result.stdout.strip()
            }

        except subprocess.TimeoutExpired:
            return {"error": "Git push command timed out", "success": False, "remote": remote, "branch": branch}
        except Exception as e:
            self.logger.error(f"Git push failed: {e}")
            return {"error": str(e), "success": False, "remote": remote, "branch": branch}

    async def _git_pull(self, remote: str = "origin", branch: str = None, **kwargs) -> Dict[str, Any]:
        """Pull changes from remote repository."""
        try:
            import subprocess

            # Get current branch if not specified
            if not branch:
                branch_result = subprocess.run(
                    ["git", "branch", "--show-current"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                branch = branch_result.stdout.strip() if branch_result.returncode == 0 else "main"

            # Run git pull
            result = subprocess.run(
                ["git", "pull", remote, branch],
                capture_output=True,
                text=True,
                timeout=60
            )

            if result.returncode != 0:
                return {
                    "error": result.stderr.strip(),
                    "success": False,
                    "remote": remote,
                    "branch": branch
                }

            output = result.stdout.strip()

            # Parse output for changes
            changes = {
                "files_changed": 0,
                "insertions": 0,
                "deletions": 0
            }

            import re
            change_match = re.search(r'(\d+) files? changed', output)
            if change_match:
                changes["files_changed"] = int(change_match.group(1))

            insertion_match = re.search(r'(\d+) insertions?', output)
            if insertion_match:
                changes["insertions"] = int(insertion_match.group(1))

            deletion_match = re.search(r'(\d+) deletions?', output)
            if deletion_match:
                changes["deletions"] = int(deletion_match.group(1))

            return {
                "success": True,
                "remote": remote,
                "branch": branch,
                "output": output,
                "changes": changes
            }

        except subprocess.TimeoutExpired:
            return {"error": "Git pull command timed out", "success": False, "remote": remote, "branch": branch}
        except Exception as e:
            self.logger.error(f"Git pull failed: {e}")
            return {"error": str(e), "success": False, "remote": remote, "branch": branch}
    
    async def _git_branch(self, name: str = None, **kwargs) -> Dict[str, Any]:
        """List or create Git branches."""
        try:
            import subprocess

            if name:
                # Create new branch
                create_and_checkout = kwargs.get("checkout", False)
                if create_and_checkout:
                    result = subprocess.run(
                        ["git", "checkout", "-b", name],
                        capture_output=True,
                        text=True,
                        timeout=30
                    )
                else:
                    result = subprocess.run(
                        ["git", "branch", name],
                        capture_output=True,
                        text=True,
                        timeout=30
                    )

                if result.returncode != 0:
                    return {
                        "error": result.stderr.strip(),
                        "success": False,
                        "branch": name
                    }

                return {
                    "success": True,
                    "branch": name,
                    "created": True,
                    "checked_out": create_and_checkout
                }
            else:
                # List branches
                result = subprocess.run(
                    ["git", "branch", "-a"],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                if result.returncode != 0:
                    return {
                        "error": result.stderr.strip(),
                        "success": False
                    }

                branches = []
                current_branch = None

                for line in result.stdout.strip().split('\n'):
                    line = line.strip()
                    if line.startswith('* '):
                        current_branch = line[2:]
                        branches.append(current_branch)
                    elif line and not line.startswith('remotes/origin/HEAD'):
                        branch_name = line.replace('remotes/origin/', '')
                        if branch_name not in branches:
                            branches.append(branch_name)

                return {
                    "success": True,
                    "branches": branches,
                    "current": current_branch
                }

        except subprocess.TimeoutExpired:
            return {"error": "Git branch command timed out", "success": False}
        except Exception as e:
            self.logger.error(f"Git branch failed: {e}")
            return {"error": str(e), "success": False}

    async def _git_checkout(self, branch: str, **kwargs) -> Dict[str, Any]:
        """Checkout Git branch."""
        try:
            import subprocess

            if not branch:
                return {"error": "Branch name is required", "success": False}

            # Check if branch exists locally
            result = subprocess.run(
                ["git", "branch", "--list", branch],
                capture_output=True,
                text=True,
                timeout=10
            )

            branch_exists = bool(result.stdout.strip())

            if not branch_exists:
                # Try to checkout from remote
                result = subprocess.run(
                    ["git", "checkout", "-b", branch, f"origin/{branch}"],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
            else:
                # Checkout existing branch
                result = subprocess.run(
                    ["git", "checkout", branch],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

            if result.returncode != 0:
                return {
                    "error": result.stderr.strip(),
                    "success": False,
                    "branch": branch
                }

            return {
                "success": True,
                "branch": branch,
                "checked_out": True,
                "created_from_remote": not branch_exists
            }

        except subprocess.TimeoutExpired:
            return {"error": "Git checkout command timed out", "success": False, "branch": branch}
        except Exception as e:
            self.logger.error(f"Git checkout failed: {e}")
            return {"error": str(e), "success": False, "branch": branch}

    async def _git_log(self, limit: int = 10, **kwargs) -> Dict[str, Any]:
        """Get Git commit log."""
        try:
            import subprocess

            # Build git log command
            cmd = ["git", "log", f"--max-count={limit}", "--pretty=format:%H|%an|%ae|%ad|%s", "--date=iso"]

            # Add file filter if specified
            file_path = kwargs.get("file")
            if file_path:
                cmd.extend(["--", file_path])

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                return {
                    "error": result.stderr.strip(),
                    "success": False
                }

            commits = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    parts = line.split('|', 4)
                    if len(parts) == 5:
                        commits.append({
                            "hash": parts[0],
                            "author": parts[1],
                            "email": parts[2],
                            "date": parts[3],
                            "message": parts[4]
                        })

            return {
                "success": True,
                "commits": commits,
                "count": len(commits),
                "file": file_path
            }

        except subprocess.TimeoutExpired:
            return {"error": "Git log command timed out", "success": False}
        except Exception as e:
            self.logger.error(f"Git log failed: {e}")
            return {"error": str(e), "success": False}

    async def _git_diff(self, file: str = None, **kwargs) -> Dict[str, Any]:
        """Show Git diff."""
        try:
            import subprocess

            # Build git diff command
            cmd = ["git", "diff"]

            # Add options
            if kwargs.get("cached", False):
                cmd.append("--cached")
            if kwargs.get("name_only", False):
                cmd.append("--name-only")

            # Add file filter if specified
            if file:
                cmd.extend(["--", file])

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                return {
                    "error": result.stderr.strip(),
                    "success": False
                }

            return {
                "success": True,
                "diff": result.stdout,
                "file": file,
                "has_changes": bool(result.stdout.strip())
            }

        except subprocess.TimeoutExpired:
            return {"error": "Git diff command timed out", "success": False}
        except Exception as e:
            self.logger.error(f"Git diff failed: {e}")
            return {"error": str(e), "success": False}

    async def _git_clone(self, url: str, directory: str = None, **kwargs) -> Dict[str, Any]:
        """Clone Git repository."""
        try:
            import subprocess
            import os

            if not url:
                return {"error": "Repository URL is required", "success": False}

            # Build git clone command
            cmd = ["git", "clone"]

            # Add options
            if kwargs.get("depth"):
                cmd.extend(["--depth", str(kwargs["depth"])])
            if kwargs.get("branch"):
                cmd.extend(["--branch", kwargs["branch"]])
            if kwargs.get("single_branch", False):
                cmd.append("--single-branch")

            cmd.append(url)

            if directory:
                cmd.append(directory)
                target_dir = directory
            else:
                # Extract directory name from URL
                target_dir = url.split('/')[-1].replace('.git', '')

            # Check if directory already exists
            if os.path.exists(target_dir):
                return {
                    "error": f"Directory '{target_dir}' already exists",
                    "success": False,
                    "url": url,
                    "directory": target_dir
                }

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes for clone
            )

            if result.returncode != 0:
                return {
                    "error": result.stderr.strip(),
                    "success": False,
                    "url": url,
                    "directory": target_dir
                }

            return {
                "success": True,
                "url": url,
                "directory": target_dir,
                "cloned": True
            }

        except subprocess.TimeoutExpired:
            return {"error": "Git clone command timed out", "success": False, "url": url}
        except Exception as e:
            self.logger.error(f"Git clone failed: {e}")
            return {"error": str(e), "success": False, "url": url}
    
    def get_schema(self) -> Dict[str, Any]:
        """Get JSON schema for Git tool."""
        return {
            "name": "git",
            "description": "Git version control operations",
            "parameters": {
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": ["status", "add", "commit", "push", "pull", "branch", "checkout", "log", "diff", "clone"],
                        "description": "Git operation to perform"
                    },
                    "message": {
                        "type": "string",
                        "description": "Commit message for commit operation"
                    },
                    "files": {
                        "type": "string",
                        "description": "Files to add (default: all)"
                    },
                    "branch": {
                        "type": "string",
                        "description": "Branch name for branch operations"
                    },
                    "url": {
                        "type": "string",
                        "description": "Repository URL for clone operation"
                    }
                },
                "required": ["operation"]
            }
        }
