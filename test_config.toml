[ai]
default_provider = "deepseek"
default_model = "deepseek-chat"

[ai.providers.deepseek]
name = "deepseek"
api_key_service = "ai-terminal-deepseek"
api_key_username = "api-key"
model = "deepseek-chat"
max_tokens = 4096
temperature = 0.7
enabled = true

[security]
encrypt_storage = true
require_approval = true
dangerous_commands = ["rm", "del", "format", "fdisk", "dd", "mkfs", "shutdown", "reboot"]

[ui]
theme = "dark"
show_timestamps = true
auto_scroll = true
syntax_highlighting = true
show_token_count = true
animation_speed = 1.0

[session]
auto_save = true
max_history = 10000
default_session = "main"

[tools]
enabled = ["shell", "file_ops", "git", "code_assist", "package_manager", "system_monitor"]
shell_timeout = 30
max_file_size = 10485760
