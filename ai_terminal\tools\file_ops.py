"""
File operations tool for AI Terminal.

Provides comprehensive file and directory operations including
read, write, create, delete, and manipulation with safety checks.
"""

import os
import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional

import aiofiles
import chardet

from ai_terminal.tools.base import BaseTool, ToolExecutionError, ToolPermissionError


class FileOperationsTool(BaseTool):
    """
    File operations tool for safe file and directory manipulation.
    
    Supports reading, writing, creating, deleting, and manipulating
    files and directories with comprehensive safety checks.
    """
    
    def __init__(self):
        """Initialize file operations tool."""
        super().__init__(
            name="file_ops",
            description="Comprehensive file and directory operations with safety checks"
        )
        self.requires_approval = False  # Individual operations may require approval
        self.max_file_size = 10 * 1024 * 1024  # 10MB default
        
        # File extensions that are considered safe
        self.safe_extensions = {
            '.txt', '.md', '.py', '.js', '.ts', '.html', '.css', '.json',
            '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.log',
            '.sh', '.bat', '.ps1', '.sql', '.xml', '.csv'
        }
    
    async def execute(self, operation: str, **kwargs) -> Any:
        """
        Execute a file operation.
        
        Args:
            operation: Type of operation (read, write, create, delete, etc.)
            **kwargs: Operation-specific arguments
            
        Returns:
            Operation result
        """
        operation_map = {
            "read": self._read_file,
            "write": self._write_file,
            "create": self._create_file,
            "delete": self._delete_file,
            "copy": self._copy_file,
            "move": self._move_file,
            "mkdir": self._create_directory,
            "rmdir": self._remove_directory,
            "list": self._list_directory,
            "exists": self._check_exists,
            "stat": self._get_file_stats,
            "search": self._search_files,
        }
        
        if operation not in operation_map:
            raise ToolExecutionError(f"Unknown operation: {operation}", self.name)
        
        try:
            return await operation_map[operation](**kwargs)
        except Exception as e:
            self.logger.error(f"File operation {operation} failed: {e}")
            raise ToolExecutionError(f"Operation {operation} failed: {e}", self.name)
    
    async def _read_file(self, path: str, encoding: str = None, max_size: int = None) -> Dict[str, Any]:
        """Read a file and return its contents."""
        file_path = Path(path).resolve()
        
        # Security checks
        await self._validate_path(file_path, "read")
        
        if not file_path.exists():
            raise ToolExecutionError(f"File does not exist: {path}", self.name)
        
        if not file_path.is_file():
            raise ToolExecutionError(f"Path is not a file: {path}", self.name)
        
        # Check file size
        file_size = file_path.stat().st_size
        max_allowed = max_size or self.max_file_size
        
        if file_size > max_allowed:
            raise ToolExecutionError(
                f"File too large: {file_size} bytes (max: {max_allowed})", 
                self.name
            )
        
        # Detect encoding if not specified
        if not encoding:
            with open(file_path, 'rb') as f:
                raw_data = f.read(min(10000, file_size))  # Read first 10KB for detection
                detected = chardet.detect(raw_data)
                encoding = detected.get('encoding', 'utf-8')
        
        try:
            async with aiofiles.open(file_path, 'r', encoding=encoding) as f:
                content = await f.read()
            
            return {
                "path": str(file_path),
                "content": content,
                "size": file_size,
                "encoding": encoding,
                "lines": len(content.splitlines()),
            }
            
        except UnicodeDecodeError as e:
            raise ToolExecutionError(f"Failed to decode file with {encoding}: {e}", self.name)
    
    async def _write_file(self, path: str, content: str, encoding: str = "utf-8", 
                         create_dirs: bool = False, backup: bool = True) -> Dict[str, Any]:
        """Write content to a file."""
        file_path = Path(path).resolve()
        
        # Security checks
        await self._validate_path(file_path, "write")
        
        # Create parent directories if requested
        if create_dirs:
            file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create backup if file exists
        backup_path = None
        if backup and file_path.exists():
            backup_path = file_path.with_suffix(file_path.suffix + '.bak')
            shutil.copy2(file_path, backup_path)
        
        try:
            async with aiofiles.open(file_path, 'w', encoding=encoding) as f:
                await f.write(content)
            
            file_size = file_path.stat().st_size
            
            return {
                "path": str(file_path),
                "size": file_size,
                "encoding": encoding,
                "lines": len(content.splitlines()),
                "backup_created": backup_path is not None,
                "backup_path": str(backup_path) if backup_path else None,
            }
            
        except Exception as e:
            # Restore backup if write failed
            if backup_path and backup_path.exists():
                shutil.copy2(backup_path, file_path)
            raise ToolExecutionError(f"Failed to write file: {e}", self.name)
    
    async def _create_file(self, path: str, content: str = "", encoding: str = "utf-8") -> Dict[str, Any]:
        """Create a new file."""
        file_path = Path(path).resolve()
        
        if file_path.exists():
            raise ToolExecutionError(f"File already exists: {path}", self.name)
        
        return await self._write_file(path, content, encoding, create_dirs=True, backup=False)
    
    async def _delete_file(self, path: str, backup: bool = True) -> Dict[str, Any]:
        """Delete a file."""
        file_path = Path(path).resolve()
        
        # Security checks
        await self._validate_path(file_path, "delete")
        
        if not file_path.exists():
            raise ToolExecutionError(f"File does not exist: {path}", self.name)
        
        if not file_path.is_file():
            raise ToolExecutionError(f"Path is not a file: {path}", self.name)
        
        # Create backup if requested
        backup_path = None
        if backup:
            backup_path = file_path.with_suffix(file_path.suffix + '.deleted')
            shutil.copy2(file_path, backup_path)
        
        file_size = file_path.stat().st_size
        file_path.unlink()
        
        return {
            "path": str(file_path),
            "deleted": True,
            "size": file_size,
            "backup_created": backup_path is not None,
            "backup_path": str(backup_path) if backup_path else None,
        }
    
    async def _copy_file(self, src: str, dst: str, overwrite: bool = False) -> Dict[str, Any]:
        """Copy a file."""
        src_path = Path(src).resolve()
        dst_path = Path(dst).resolve()
        
        # Security checks
        await self._validate_path(src_path, "read")
        await self._validate_path(dst_path, "write")
        
        if not src_path.exists():
            raise ToolExecutionError(f"Source file does not exist: {src}", self.name)
        
        if dst_path.exists() and not overwrite:
            raise ToolExecutionError(f"Destination exists and overwrite=False: {dst}", self.name)
        
        # Create destination directory if needed
        dst_path.parent.mkdir(parents=True, exist_ok=True)
        
        shutil.copy2(src_path, dst_path)
        
        return {
            "src": str(src_path),
            "dst": str(dst_path),
            "size": dst_path.stat().st_size,
            "copied": True,
        }
    
    async def _move_file(self, src: str, dst: str, overwrite: bool = False) -> Dict[str, Any]:
        """Move a file."""
        src_path = Path(src).resolve()
        dst_path = Path(dst).resolve()
        
        # Security checks
        await self._validate_path(src_path, "delete")
        await self._validate_path(dst_path, "write")
        
        if not src_path.exists():
            raise ToolExecutionError(f"Source file does not exist: {src}", self.name)
        
        if dst_path.exists() and not overwrite:
            raise ToolExecutionError(f"Destination exists and overwrite=False: {dst}", self.name)
        
        # Create destination directory if needed
        dst_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_size = src_path.stat().st_size
        shutil.move(src_path, dst_path)
        
        return {
            "src": str(src_path),
            "dst": str(dst_path),
            "size": file_size,
            "moved": True,
        }
    
    async def _create_directory(self, path: str, parents: bool = True) -> Dict[str, Any]:
        """Create a directory."""
        dir_path = Path(path).resolve()
        
        # Security checks
        await self._validate_path(dir_path, "write")
        
        if dir_path.exists():
            if dir_path.is_dir():
                return {"path": str(dir_path), "created": False, "existed": True}
            else:
                raise ToolExecutionError(f"Path exists but is not a directory: {path}", self.name)
        
        dir_path.mkdir(parents=parents, exist_ok=True)
        
        return {
            "path": str(dir_path),
            "created": True,
            "parents": parents,
        }
    
    async def _remove_directory(self, path: str, recursive: bool = False) -> Dict[str, Any]:
        """Remove a directory."""
        dir_path = Path(path).resolve()
        
        # Security checks
        await self._validate_path(dir_path, "delete")
        
        if not dir_path.exists():
            raise ToolExecutionError(f"Directory does not exist: {path}", self.name)
        
        if not dir_path.is_dir():
            raise ToolExecutionError(f"Path is not a directory: {path}", self.name)
        
        # Check if directory is empty
        if not recursive and any(dir_path.iterdir()):
            raise ToolExecutionError(f"Directory not empty (use recursive=True): {path}", self.name)
        
        if recursive:
            shutil.rmtree(dir_path)
        else:
            dir_path.rmdir()
        
        return {
            "path": str(dir_path),
            "removed": True,
            "recursive": recursive,
        }
    
    async def _list_directory(self, path: str = ".", pattern: str = "*", 
                            include_hidden: bool = False) -> Dict[str, Any]:
        """List directory contents."""
        dir_path = Path(path).resolve()
        
        # Security checks
        await self._validate_path(dir_path, "read")
        
        if not dir_path.exists():
            raise ToolExecutionError(f"Directory does not exist: {path}", self.name)
        
        if not dir_path.is_dir():
            raise ToolExecutionError(f"Path is not a directory: {path}", self.name)
        
        items = []
        for item in dir_path.glob(pattern):
            if not include_hidden and item.name.startswith('.'):
                continue
            
            stat = item.stat()
            items.append({
                "name": item.name,
                "path": str(item),
                "type": "directory" if item.is_dir() else "file",
                "size": stat.st_size if item.is_file() else None,
                "modified": stat.st_mtime,
                "permissions": oct(stat.st_mode)[-3:],
            })
        
        return {
            "path": str(dir_path),
            "items": sorted(items, key=lambda x: (x["type"], x["name"])),
            "count": len(items),
        }
    
    async def _check_exists(self, path: str) -> Dict[str, Any]:
        """Check if a path exists."""
        file_path = Path(path).resolve()
        
        exists = file_path.exists()
        result = {
            "path": str(file_path),
            "exists": exists,
        }
        
        if exists:
            result.update({
                "type": "directory" if file_path.is_dir() else "file",
                "size": file_path.stat().st_size if file_path.is_file() else None,
            })
        
        return result
    
    async def _get_file_stats(self, path: str) -> Dict[str, Any]:
        """Get detailed file statistics."""
        file_path = Path(path).resolve()
        
        if not file_path.exists():
            raise ToolExecutionError(f"Path does not exist: {path}", self.name)
        
        stat = file_path.stat()
        
        return {
            "path": str(file_path),
            "type": "directory" if file_path.is_dir() else "file",
            "size": stat.st_size,
            "created": stat.st_ctime,
            "modified": stat.st_mtime,
            "accessed": stat.st_atime,
            "permissions": oct(stat.st_mode)[-3:],
            "owner": stat.st_uid,
            "group": stat.st_gid,
        }
    
    async def _search_files(self, directory: str = ".", pattern: str = "*", 
                          content: str = None, max_depth: int = 5) -> Dict[str, Any]:
        """Search for files by name or content."""
        dir_path = Path(directory).resolve()
        
        # Security checks
        await self._validate_path(dir_path, "read")
        
        if not dir_path.exists() or not dir_path.is_dir():
            raise ToolExecutionError(f"Invalid directory: {directory}", self.name)
        
        matches = []
        
        def search_recursive(path: Path, depth: int = 0):
            if depth > max_depth:
                return
            
            try:
                for item in path.iterdir():
                    if item.is_file() and item.match(pattern):
                        match_info = {
                            "path": str(item),
                            "name": item.name,
                            "size": item.stat().st_size,
                            "modified": item.stat().st_mtime,
                        }
                        
                        # Search content if specified
                        if content:
                            try:
                                with open(item, 'r', encoding='utf-8', errors='ignore') as f:
                                    file_content = f.read()
                                    if content.lower() in file_content.lower():
                                        match_info["content_match"] = True
                                        matches.append(match_info)
                            except Exception:
                                pass  # Skip files that can't be read
                        else:
                            matches.append(match_info)
                    
                    elif item.is_dir() and not item.name.startswith('.'):
                        search_recursive(item, depth + 1)
                        
            except PermissionError:
                pass  # Skip directories we can't access
        
        search_recursive(dir_path)
        
        return {
            "directory": str(dir_path),
            "pattern": pattern,
            "content_search": content,
            "matches": matches,
            "count": len(matches),
        }
    
    async def _validate_path(self, path: Path, operation: str) -> None:
        """Validate path for security."""
        # Check for path traversal
        try:
            path.resolve().relative_to(Path.cwd().resolve())
        except ValueError:
            # Allow absolute paths within reasonable bounds
            if not str(path).startswith(('/home/', '/tmp/', '/var/tmp/', 'C:\\Users\\<USER>\\temp\\')):
                raise ToolPermissionError(f"Path outside allowed directories: {path}", self.name)
        
        # Check file extension for write operations
        if operation in ("write", "create") and path.suffix:
            if path.suffix.lower() not in self.safe_extensions:
                self.requires_approval = True
    
    def is_dangerous(self, args: Dict[str, Any]) -> bool:
        """Check if operation is potentially dangerous."""
        operation = args.get("operation", "")
        path = args.get("path", "")
        
        # Delete operations are always dangerous
        if operation in ("delete", "rmdir"):
            return True
        
        # Writing to system directories
        dangerous_paths = [
            "/etc/", "/bin/", "/sbin/", "/usr/bin/", "/usr/sbin/",
            "C:\\Windows\\", "C:\\Program Files\\", "C:\\System32\\"
        ]
        
        for dangerous_path in dangerous_paths:
            if path.startswith(dangerous_path):
                return True
        
        return False
    
    def get_schema(self) -> Dict[str, Any]:
        """Get JSON schema for file operations tool."""
        return {
            "name": "file_ops",
            "description": "Comprehensive file and directory operations",
            "parameters": {
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": ["read", "write", "create", "delete", "copy", "move", 
                               "mkdir", "rmdir", "list", "exists", "stat", "search"],
                        "description": "Type of file operation to perform"
                    },
                    "path": {
                        "type": "string",
                        "description": "File or directory path"
                    },
                    "content": {
                        "type": "string",
                        "description": "Content for write operations"
                    },
                    "encoding": {
                        "type": "string",
                        "default": "utf-8",
                        "description": "Text encoding for file operations"
                    }
                },
                "required": ["operation", "path"]
            }
        }
