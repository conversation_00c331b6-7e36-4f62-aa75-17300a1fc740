"""
Main terminal interface for AI Terminal.

Provides the primary user interface using Textual with rich terminal components,
chat interface, and interactive elements for autonomous AI interaction.
"""

import asyncio
from typing import Optional

from textual import on
from textual.app import App, ComposeResult
from textual.containers import Container, Horizontal, Vertical
from textual.widgets import <PERSON><PERSON>, <PERSON><PERSON>, Static

from ai_terminal.agents.engine import AgentEngine
from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin
from ai_terminal.storage.session import SessionManager
from ai_terminal.tools.manager import ToolManager
from ai_terminal.ui.components.chat import TerminalChat
from ai_terminal.ui.components.header import TerminalHeader
from ai_terminal.ui.components.input import TerminalChatInput
from ai_terminal.ui.components.overlays import (
    ApprovalModeOverlay,
    DiffOverlay,
    HelpOverlay,
    HistoryOverlay,
    ModelOverlay,
    SessionsOverlay,
)


class TerminalApp(App, LoggerMixin):
    """
    Main terminal application using Textual.
    
    Provides a rich terminal interface with chat, input, overlays,
    and comprehensive keyboard shortcuts for efficient interaction.
    """
    
    CSS = """
    Screen {
        background: $background;
    }
    
    #main-container {
        height: 100%;
        width: 100%;
    }
    
    #chat-container {
        height: 1fr;
        width: 100%;
        border: solid $primary;
        border-title-align: left;
    }
    
    #input-container {
        height: auto;
        width: 100%;
        min-height: 3;
        max-height: 10;
        border: solid $accent;
        border-title-align: left;
    }
    
    .overlay {
        background: $background 90%;
        border: solid $primary;
        border-title-align: center;
    }
    
    .status-bar {
        background: $primary;
        color: $text;
        height: 1;
    }
    
    .thinking {
        background: $warning;
        color: $text;
        height: 1;
    }
    """
    
    BINDINGS = [
        ("ctrl+c", "quit", "Quit"),
        ("ctrl+n", "new_session", "New Session"),
        ("ctrl+o", "open_session", "Open Session"),
        ("ctrl+s", "save_session", "Save Session"),
        ("ctrl+h", "show_help", "Help"),
        ("ctrl+m", "show_models", "Models"),
        ("ctrl+d", "show_diff", "Diff"),
        ("ctrl+r", "show_history", "History"),
        ("f1", "show_help", "Help"),
        ("f2", "show_models", "Models"),
        ("f3", "show_sessions", "Sessions"),
        ("f4", "show_history", "History"),
        ("f5", "refresh", "Refresh"),
        ("escape", "close_overlay", "Close"),
    ]
    
    def __init__(
        self,
        config: Config,
        agent_engine: AgentEngine,
        session_manager: SessionManager,
        tool_manager: ToolManager,
        approval_manager=None,
    ):
        """Initialize the terminal application."""
        super().__init__()
        
        self.config = config
        self.agent_engine = agent_engine
        self.session_manager = session_manager
        self.tool_manager = tool_manager
        self.approval_manager = approval_manager
        
        # UI components
        self.terminal_header: Optional[TerminalHeader] = None
        self.terminal_chat: Optional[TerminalChat] = None
        self.terminal_input: Optional[TerminalChatInput] = None
        
        # Overlays
        self.help_overlay: Optional[HelpOverlay] = None
        self.model_overlay: Optional[ModelOverlay] = None
        self.sessions_overlay: Optional[SessionsOverlay] = None
        self.history_overlay: Optional[HistoryOverlay] = None
        self.diff_overlay: Optional[DiffOverlay] = None
        self.approval_overlay: Optional[ApprovalModeOverlay] = None
        
        # State
        self.current_overlay: Optional[str] = None
        self.is_thinking = False
        
        self.logger.info("Terminal application initialized")
    
    def compose(self) -> ComposeResult:
        """Compose the terminal UI layout."""
        # Main header
        self.terminal_header = TerminalHeader(
            config=self.config,
            session_manager=self.session_manager,
        )
        yield self.terminal_header
        
        # Main container
        with Container(id="main-container"):
            # Chat display area
            with Container(id="chat-container"):
                self.terminal_chat = TerminalChat(
                    config=self.config,
                    agent_engine=self.agent_engine,
                    session_manager=self.session_manager,
                )
                yield self.terminal_chat
            
            # Input area
            with Container(id="input-container"):
                self.terminal_input = TerminalChatInput(
                    config=self.config,
                    agent_engine=self.agent_engine,
                    terminal_chat=self.terminal_chat,
                )
                yield self.terminal_input
        
        # Footer with shortcuts
        yield Footer()
    
    async def on_mount(self) -> None:
        """Handle application mount."""
        self.logger.debug("Terminal application mounted")
        
        # Set initial focus to input
        if self.terminal_input:
            self.terminal_input.focus()
        
        # Load current session
        await self._load_current_session()
        
        # Setup agent callbacks
        self._setup_agent_callbacks()
    
    async def _load_current_session(self) -> None:
        """Load the current session into the chat display."""
        try:
            current_session = await self.session_manager.get_current_session()
            if current_session and self.terminal_chat:
                await self.terminal_chat.load_session(current_session)
        except Exception as e:
            self.logger.error(f"Failed to load current session: {e}")
    
    def _setup_agent_callbacks(self) -> None:
        """Setup callbacks for agent events."""
        if self.agent_engine:
            self.agent_engine.on_thinking_start = self._on_thinking_start
            self.agent_engine.on_thinking_stop = self._on_thinking_stop
            self.agent_engine.on_tool_call = self._on_tool_call
            self.agent_engine.on_approval_required = self._on_approval_required
    
    async def _on_thinking_start(self) -> None:
        """Handle agent thinking start."""
        self.is_thinking = True
        if self.terminal_header:
            await self.terminal_header.set_thinking(True)
    
    async def _on_thinking_stop(self) -> None:
        """Handle agent thinking stop."""
        self.is_thinking = False
        if self.terminal_header:
            await self.terminal_header.set_thinking(False)
    
    async def _on_tool_call(self, tool_name: str, args: dict) -> None:
        """Handle tool call event."""
        if self.terminal_chat:
            await self.terminal_chat.show_tool_call(tool_name, args)
    
    async def _on_approval_required(self, command: str, description: str) -> bool:
        """Handle approval required event."""
        if not self.approval_overlay:
            self.approval_overlay = ApprovalModeOverlay(
                command=command,
                description=description,
            )
        
        await self._show_overlay("approval", self.approval_overlay)
        return await self.approval_overlay.get_approval()
    
    async def action_quit(self) -> None:
        """Quit the application."""
        await self.shutdown()
        self.exit()
    
    async def action_new_session(self) -> None:
        """Create a new session."""
        try:
            session_name = await self._prompt_session_name()
            if session_name:
                await self.session_manager.create_session(session_name)
                if self.terminal_chat:
                    await self.terminal_chat.clear()
                if self.terminal_header:
                    await self.terminal_header.update_session_info()
        except Exception as e:
            self.logger.error(f"Failed to create new session: {e}")
    
    async def action_open_session(self) -> None:
        """Open session selection overlay."""
        if not self.sessions_overlay:
            self.sessions_overlay = SessionsOverlay(
                session_manager=self.session_manager,
                on_session_selected=self._on_session_selected,
            )
        await self._show_overlay("sessions", self.sessions_overlay)
    
    async def action_save_session(self) -> None:
        """Save current session."""
        try:
            await self.session_manager.save_current_session()
            if self.terminal_header:
                await self.terminal_header.show_status("Session saved", "success")
        except Exception as e:
            self.logger.error(f"Failed to save session: {e}")
            if self.terminal_header:
                await self.terminal_header.show_status("Save failed", "error")
    
    async def action_show_help(self) -> None:
        """Show help overlay."""
        if not self.help_overlay:
            self.help_overlay = HelpOverlay()
        await self._show_overlay("help", self.help_overlay)
    
    async def action_show_models(self) -> None:
        """Show model selection overlay."""
        if not self.model_overlay:
            self.model_overlay = ModelOverlay(
                config=self.config,
                agent_engine=self.agent_engine,
            )
        await self._show_overlay("models", self.model_overlay)
    
    async def action_show_diff(self) -> None:
        """Show diff overlay."""
        if not self.diff_overlay:
            self.diff_overlay = DiffOverlay()
        await self._show_overlay("diff", self.diff_overlay)
    
    async def action_show_history(self) -> None:
        """Show history overlay."""
        if not self.history_overlay:
            self.history_overlay = HistoryOverlay(
                session_manager=self.session_manager,
            )
        await self._show_overlay("history", self.history_overlay)
    
    async def action_show_sessions(self) -> None:
        """Show sessions overlay."""
        await self.action_open_session()
    
    async def action_refresh(self) -> None:
        """Refresh the interface."""
        if self.terminal_header:
            await self.terminal_header.refresh()
        if self.terminal_chat:
            await self.terminal_chat.refresh()
    
    async def action_close_overlay(self) -> None:
        """Close current overlay."""
        await self._close_overlay()
    
    async def _show_overlay(self, overlay_name: str, overlay_widget) -> None:
        """Show an overlay."""
        if self.current_overlay:
            await self._close_overlay()
        
        self.current_overlay = overlay_name
        overlay_widget.add_class("overlay")
        await self.mount(overlay_widget)
        overlay_widget.focus()
    
    async def _close_overlay(self) -> None:
        """Close current overlay."""
        if self.current_overlay:
            overlay_widget = getattr(self, f"{self.current_overlay}_overlay", None)
            if overlay_widget and overlay_widget.parent:
                await overlay_widget.remove()
            self.current_overlay = None
            
            # Return focus to input
            if self.terminal_input:
                self.terminal_input.focus()
    
    async def _on_session_selected(self, session_name: str) -> None:
        """Handle session selection."""
        try:
            await self.session_manager.load_session(session_name)
            if self.terminal_chat:
                current_session = await self.session_manager.get_current_session()
                await self.terminal_chat.load_session(current_session)
            if self.terminal_header:
                await self.terminal_header.update_session_info()
            await self._close_overlay()
        except Exception as e:
            self.logger.error(f"Failed to load session: {e}")
    
    async def _prompt_session_name(self) -> Optional[str]:
        """Prompt for session name."""
        # This would typically show an input dialog
        # For now, generate a default name
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"session_{timestamp}"
    
    async def shutdown(self) -> None:
        """Shutdown the terminal application."""
        self.logger.info("Shutting down terminal application")
        
        try:
            # Save current session
            if self.session_manager:
                await self.session_manager.save_current_session()
            
            # Cleanup components
            if self.terminal_chat:
                await self.terminal_chat.cleanup()
            
            if self.terminal_input:
                await self.terminal_input.cleanup()
            
        except Exception as e:
            self.logger.error(f"Error during terminal shutdown: {e}")
    
    async def run(self) -> None:
        """Run the terminal application."""
        try:
            await self.run_async()
        except Exception as e:
            self.logger.error(f"Terminal application error: {e}")
            raise
