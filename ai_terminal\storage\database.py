"""
Database manager for AI Terminal.

Handles SQLite database operations, migrations, and connection management
with support for encryption and backup/restore functionality.
"""

import asyncio
import os
import shutil
from pathlib import Path
from typing import Optional

from sqlalchemy import create_engine, event
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin
from ai_terminal.storage.models import Base


class DatabaseManager(LoggerMixin):
    """
    Database manager for AI Terminal.
    
    Manages SQLite database connections, migrations, and operations
    with support for encryption and backup functionality.
    """
    
    def __init__(self, config: Config):
        """Initialize database manager."""
        self.config = config
        self.db_path = self._get_database_path()
        self.engine: Optional[Engine] = None
        self.async_engine = None
        self.session_factory = None
        self.async_session_factory = None
        
        # Ensure database directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
    
    def _get_database_path(self) -> Path:
        """Get the database file path."""
        db_dir = self.config.config_dir / "data"
        db_dir.mkdir(exist_ok=True)
        return db_dir / "ai_terminal.db"
    
    async def initialize(self) -> None:
        """Initialize the database."""
        try:
            # Create synchronous engine for migrations
            self.engine = create_engine(
                f"sqlite:///{self.db_path}",
                echo=False,
                pool_pre_ping=True,
                connect_args={"check_same_thread": False}
            )
            
            # Enable WAL mode for better concurrency
            @event.listens_for(self.engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA journal_mode=WAL")
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.execute("PRAGMA cache_size=10000")
                cursor.execute("PRAGMA temp_store=MEMORY")
                cursor.execute("PRAGMA mmap_size=268435456")  # 256MB
                cursor.close()
            
            # Create async engine
            self.async_engine = create_async_engine(
                f"sqlite+aiosqlite:///{self.db_path}",
                echo=False,
                pool_pre_ping=True,
            )
            
            # Create session factories
            self.session_factory = sessionmaker(
                bind=self.engine,
                expire_on_commit=False
            )
            
            self.async_session_factory = sessionmaker(
                bind=self.async_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Create tables
            await self._create_tables()
            
            # Run migrations
            await self._run_migrations()
            
            self.logger.info(f"Database initialized at {self.db_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def _create_tables(self) -> None:
        """Create database tables."""
        try:
            # Use sync engine for table creation
            Base.metadata.create_all(self.engine)
            self.logger.debug("Database tables created")
        except Exception as e:
            self.logger.error(f"Failed to create tables: {e}")
            raise
    
    async def _run_migrations(self) -> None:
        """Run database migrations."""
        try:
            # Check current schema version
            current_version = await self._get_schema_version()
            target_version = 1  # Current schema version
            
            if current_version < target_version:
                self.logger.info(f"Running migrations from v{current_version} to v{target_version}")
                
                # Run migration scripts
                for version in range(current_version + 1, target_version + 1):
                    await self._run_migration(version)
                
                # Update schema version
                await self._set_schema_version(target_version)
                
                self.logger.info("Database migrations completed")
            
        except Exception as e:
            self.logger.error(f"Failed to run migrations: {e}")
            raise
    
    async def _get_schema_version(self) -> int:
        """Get current database schema version."""
        try:
            with self.session_factory() as session:
                result = session.execute(
                    "SELECT name FROM sqlite_master WHERE type='table' AND name='schema_version'"
                )
                if not result.fetchone():
                    # Create schema_version table
                    session.execute(
                        "CREATE TABLE schema_version (version INTEGER PRIMARY KEY)"
                    )
                    session.execute("INSERT INTO schema_version (version) VALUES (0)")
                    session.commit()
                    return 0
                
                result = session.execute("SELECT version FROM schema_version LIMIT 1")
                row = result.fetchone()
                return row[0] if row else 0
                
        except Exception as e:
            self.logger.error(f"Failed to get schema version: {e}")
            return 0
    
    async def _set_schema_version(self, version: int) -> None:
        """Set database schema version."""
        try:
            with self.session_factory() as session:
                session.execute(
                    "UPDATE schema_version SET version = ?", (version,)
                )
                session.commit()
        except Exception as e:
            self.logger.error(f"Failed to set schema version: {e}")
    
    async def _run_migration(self, version: int) -> None:
        """Run a specific migration."""
        self.logger.info(f"Running migration v{version}")
        
        # Migration scripts would go here
        # For now, we only have v1 which is the initial schema
        if version == 1:
            # Initial schema is already created by create_tables
            pass
    
    def get_session(self):
        """Get a synchronous database session."""
        if not self.session_factory:
            raise RuntimeError("Database not initialized")
        return self.session_factory()
    
    def get_async_session(self):
        """Get an asynchronous database session."""
        if not self.async_session_factory:
            raise RuntimeError("Database not initialized")
        return self.async_session_factory()
    
    async def backup_database(self, backup_path: Optional[Path] = None) -> Path:
        """Create a backup of the database."""
        try:
            if backup_path is None:
                timestamp = asyncio.get_event_loop().time()
                backup_name = f"ai_terminal_backup_{int(timestamp)}.db"
                backup_path = self.config.config_dir / "backups" / backup_name
            
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Close connections temporarily
            if self.engine:
                self.engine.dispose()
            if self.async_engine:
                await self.async_engine.dispose()
            
            # Copy database file
            shutil.copy2(self.db_path, backup_path)
            
            # Reinitialize connections
            await self.initialize()
            
            self.logger.info(f"Database backed up to {backup_path}")
            return backup_path
            
        except Exception as e:
            self.logger.error(f"Failed to backup database: {e}")
            raise
    
    async def restore_database(self, backup_path: Path) -> None:
        """Restore database from backup."""
        try:
            if not backup_path.exists():
                raise FileNotFoundError(f"Backup file not found: {backup_path}")
            
            # Close connections
            if self.engine:
                self.engine.dispose()
            if self.async_engine:
                await self.async_engine.dispose()
            
            # Create backup of current database
            current_backup = await self.backup_database()
            self.logger.info(f"Current database backed up to {current_backup}")
            
            # Restore from backup
            shutil.copy2(backup_path, self.db_path)
            
            # Reinitialize
            await self.initialize()
            
            self.logger.info(f"Database restored from {backup_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to restore database: {e}")
            raise
    
    async def vacuum_database(self) -> None:
        """Vacuum the database to reclaim space."""
        try:
            with self.session_factory() as session:
                session.execute("VACUUM")
                session.commit()
            
            self.logger.info("Database vacuumed successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to vacuum database: {e}")
            raise
    
    async def get_database_stats(self) -> dict:
        """Get database statistics."""
        try:
            stats = {}
            
            with self.session_factory() as session:
                # Database size
                stats["file_size"] = self.db_path.stat().st_size
                
                # Table counts
                result = session.execute("SELECT COUNT(*) FROM sessions")
                stats["session_count"] = result.scalar()
                
                result = session.execute("SELECT COUNT(*) FROM messages")
                stats["message_count"] = result.scalar()
                
                result = session.execute("SELECT COUNT(*) FROM tool_usage")
                stats["tool_usage_count"] = result.scalar()
                
                # Schema version
                result = session.execute("SELECT version FROM schema_version LIMIT 1")
                stats["schema_version"] = result.scalar()
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get database stats: {e}")
            return {}
    
    async def shutdown(self) -> None:
        """Shutdown database connections."""
        try:
            if self.engine:
                self.engine.dispose()
                self.engine = None
            
            if self.async_engine:
                await self.async_engine.dispose()
                self.async_engine = None
            
            self.session_factory = None
            self.async_session_factory = None
            
            self.logger.info("Database connections closed")
            
        except Exception as e:
            self.logger.error(f"Error shutting down database: {e}")

    async def clear_session_messages(self, session_id: str) -> None:
        """Clear all messages from a session."""
        try:
            async with self.get_async_session() as session:
                await session.execute(
                    "DELETE FROM messages WHERE session_id = :session_id",
                    {"session_id": session_id}
                )
                await session.commit()

            self.logger.debug(f"Cleared messages for session: {session_id}")

        except Exception as e:
            self.logger.error(f"Failed to clear session messages: {e}")
            raise
