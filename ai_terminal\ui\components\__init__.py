"""
UI Components for AI Terminal.

Contains all the modular terminal UI components including chat interface,
input handling, overlays, and interactive elements.
"""

from ai_terminal.ui.components.chat import TerminalChat, MessageHistory, TerminalChatResponseItem, TerminalChatToolCallCommand
from ai_terminal.ui.components.header import TerminalHeader
from ai_terminal.ui.components.input import TerminalChatInput, TerminalChatInputAnimativeThinking
from ai_terminal.ui.components.multiline import MultilineEditor
from ai_terminal.ui.components.completions import CompletionsOverlay, TypeaheadOverlay
from ai_terminal.ui.components.overlays import (
    ApprovalModeOverlay,
    DiffOverlay,
    HelpOverlay,
    HistoryOverlay,
    ModelOverlay,
    SessionsOverlay,
    TerminalChatCommandReview,
)

__all__ = [
    "TerminalChat",
    "TerminalHeader",
    "TerminalChatInput",
    "TerminalChatInputAnimativeThinking",
    "TerminalChatResponseItem",
    "TerminalChatToolCallCommand",
    "MessageHistory",
    "MultilineEditor",
    "CompletionsOverlay",
    "TypeaheadOverlay",
    "ApprovalModeOverlay",
    "DiffOverlay",
    "HelpOverlay",
    "HistoryOverlay",
    "ModelOverlay",
    "SessionsOverlay",
    "TerminalChatCommandReview",
]
