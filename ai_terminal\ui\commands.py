"""
Slash command system for AI Terminal.

Provides comprehensive slash command parsing and execution for terminal operations
including session management, configuration, and utility functions.
"""

import asyncio
import json
import shlex
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin


class SlashCommand:
    """Represents a single slash command."""
    
    def __init__(
        self,
        name: str,
        description: str,
        handler: callable,
        aliases: Optional[List[str]] = None,
        args: Optional[List[Dict[str, Any]]] = None,
        requires_confirmation: bool = False,
    ):
        """Initialize slash command."""
        self.name = name
        self.description = description
        self.handler = handler
        self.aliases = aliases or []
        self.args = args or []
        self.requires_confirmation = requires_confirmation


class SlashCommandProcessor(LoggerMixin):
    """
    Slash command processor for AI Terminal.
    
    Handles parsing, validation, and execution of slash commands
    with support for arguments, aliases, and confirmation workflows.
    """
    
    def __init__(
        self,
        config: Config,
        session_manager=None,
        agent_engine=None,
        terminal_app=None,
    ):
        """Initialize slash command processor."""
        self.config = config
        self.session_manager = session_manager
        self.agent_engine = agent_engine
        self.terminal_app = terminal_app
        
        # Command registry
        self.commands: Dict[str, SlashCommand] = {}
        self.aliases: Dict[str, str] = {}
        
        # Register built-in commands
        self._register_builtin_commands()
    
    def _register_builtin_commands(self) -> None:
        """Register built-in slash commands."""
        commands = [
            SlashCommand(
                name="help",
                description="Show help information for commands",
                handler=self._cmd_help,
                aliases=["h", "?"],
                args=[{"name": "command", "type": "str", "required": False, "description": "Command to get help for"}]
            ),
            SlashCommand(
                name="clear",
                description="Clear the chat history",
                handler=self._cmd_clear,
                aliases=["cls"],
                requires_confirmation=True
            ),
            SlashCommand(
                name="compact",
                description="Compact conversation history to save context",
                handler=self._cmd_compact,
                aliases=["compress"]
            ),
            SlashCommand(
                name="history",
                description="Show conversation history",
                handler=self._cmd_history,
                aliases=["hist"],
                args=[
                    {"name": "limit", "type": "int", "required": False, "default": 10, "description": "Number of messages to show"},
                    {"name": "search", "type": "str", "required": False, "description": "Search term to filter messages"}
                ]
            ),
            SlashCommand(
                name="sessions",
                description="Manage conversation sessions",
                handler=self._cmd_sessions,
                aliases=["session", "sess"],
                args=[
                    {"name": "action", "type": "str", "required": False, "choices": ["list", "new", "load", "delete", "rename"], "default": "list"},
                    {"name": "name", "type": "str", "required": False, "description": "Session name for actions"}
                ]
            ),
            SlashCommand(
                name="model",
                description="Switch AI model or provider",
                handler=self._cmd_model,
                aliases=["provider"],
                args=[
                    {"name": "model", "type": "str", "required": False, "description": "Model name to switch to"},
                    {"name": "provider", "type": "str", "required": False, "description": "Provider name to switch to"}
                ]
            ),
            SlashCommand(
                name="diff",
                description="Show file differences",
                handler=self._cmd_diff,
                args=[
                    {"name": "file1", "type": "str", "required": True, "description": "First file to compare"},
                    {"name": "file2", "type": "str", "required": False, "description": "Second file to compare (defaults to working copy)"}
                ]
            ),
            SlashCommand(
                name="config",
                description="View or modify configuration",
                handler=self._cmd_config,
                aliases=["cfg"],
                args=[
                    {"name": "key", "type": "str", "required": False, "description": "Configuration key to view/set"},
                    {"name": "value", "type": "str", "required": False, "description": "Value to set (omit to view)"}
                ]
            ),
            SlashCommand(
                name="export",
                description="Export conversation or session data",
                handler=self._cmd_export,
                args=[
                    {"name": "format", "type": "str", "required": False, "choices": ["json", "markdown", "text"], "default": "json"},
                    {"name": "file", "type": "str", "required": False, "description": "Output file path"},
                    {"name": "session", "type": "str", "required": False, "description": "Session to export (current if not specified)"}
                ]
            ),
            SlashCommand(
                name="import",
                description="Import conversation or session data",
                handler=self._cmd_import,
                args=[
                    {"name": "file", "type": "str", "required": True, "description": "File to import"},
                    {"name": "format", "type": "str", "required": False, "choices": ["json", "markdown"], "default": "json"}
                ]
            ),
            SlashCommand(
                name="status",
                description="Show system status and statistics",
                handler=self._cmd_status,
                aliases=["stat", "info"]
            ),
            SlashCommand(
                name="reset",
                description="Reset the current session",
                handler=self._cmd_reset,
                requires_confirmation=True
            ),
            SlashCommand(
                name="quit",
                description="Exit the application",
                handler=self._cmd_quit,
                aliases=["exit", "q"],
                requires_confirmation=True
            ),
        ]
        
        # Register commands and aliases
        for cmd in commands:
            self.commands[cmd.name] = cmd
            for alias in cmd.aliases:
                self.aliases[alias] = cmd.name
    
    def is_slash_command(self, text: str) -> bool:
        """Check if text is a slash command."""
        return text.strip().startswith('/')
    
    def parse_command(self, text: str) -> Tuple[str, List[str], Dict[str, Any]]:
        """
        Parse slash command text into command name, args, and kwargs.
        
        Returns:
            Tuple of (command_name, positional_args, keyword_args)
        """
        if not self.is_slash_command(text):
            raise ValueError("Not a slash command")
        
        # Remove leading slash and parse
        command_text = text[1:].strip()
        if not command_text:
            raise ValueError("Empty command")
        
        # Use shlex to properly handle quoted arguments
        try:
            parts = shlex.split(command_text)
        except ValueError as e:
            raise ValueError(f"Invalid command syntax: {e}")
        
        command_name = parts[0].lower()
        args = parts[1:]
        
        # Resolve aliases
        if command_name in self.aliases:
            command_name = self.aliases[command_name]
        
        # Parse arguments into positional and keyword
        positional_args = []
        keyword_args = {}
        
        i = 0
        while i < len(args):
            arg = args[i]
            if arg.startswith('--'):
                # Long option
                key = arg[2:]
                if '=' in key:
                    key, value = key.split('=', 1)
                    keyword_args[key] = value
                else:
                    # Next argument is the value
                    if i + 1 < len(args) and not args[i + 1].startswith('-'):
                        keyword_args[key] = args[i + 1]
                        i += 1
                    else:
                        keyword_args[key] = True
            elif arg.startswith('-') and len(arg) > 1:
                # Short option
                key = arg[1:]
                if i + 1 < len(args) and not args[i + 1].startswith('-'):
                    keyword_args[key] = args[i + 1]
                    i += 1
                else:
                    keyword_args[key] = True
            else:
                # Positional argument
                positional_args.append(arg)
            i += 1
        
        return command_name, positional_args, keyword_args
    
    async def execute_command(self, text: str) -> Dict[str, Any]:
        """
        Execute a slash command.
        
        Returns:
            Command execution result
        """
        try:
            command_name, pos_args, kwargs = self.parse_command(text)
            
            if command_name not in self.commands:
                return {
                    "success": False,
                    "error": f"Unknown command: {command_name}",
                    "suggestion": self._suggest_command(command_name)
                }
            
            command = self.commands[command_name]
            
            # Check if confirmation is required
            if command.requires_confirmation:
                confirmation = kwargs.get('force', False) or kwargs.get('y', False)
                if not confirmation:
                    return {
                        "success": False,
                        "requires_confirmation": True,
                        "command": command_name,
                        "message": f"Command '{command_name}' requires confirmation. Use --force or -y to proceed."
                    }
            
            # Execute command
            result = await command.handler(pos_args, kwargs)
            return {
                "success": True,
                "command": command_name,
                "result": result
            }
            
        except Exception as e:
            self.logger.error(f"Command execution error: {e}")
            return {
                "success": False,
                "error": str(e),
                "command": text
            }
    
    def _suggest_command(self, command_name: str) -> Optional[str]:
        """Suggest a similar command name."""
        # Simple fuzzy matching
        all_commands = list(self.commands.keys()) + list(self.aliases.keys())
        
        # Find commands that start with the same letter
        candidates = [cmd for cmd in all_commands if cmd.startswith(command_name[0])]
        
        if candidates:
            return f"Did you mean: {', '.join(candidates[:3])}?"
        
        return None
    
    def get_command_help(self, command_name: Optional[str] = None) -> str:
        """Get help text for commands."""
        if command_name:
            if command_name in self.aliases:
                command_name = self.aliases[command_name]
            
            if command_name not in self.commands:
                return f"Unknown command: {command_name}"
            
            cmd = self.commands[command_name]
            help_text = f"/{cmd.name} - {cmd.description}\n"
            
            if cmd.aliases:
                help_text += f"Aliases: {', '.join(cmd.aliases)}\n"
            
            if cmd.args:
                help_text += "\nArguments:\n"
                for arg in cmd.args:
                    required = " (required)" if arg.get("required", False) else ""
                    default = f" [default: {arg['default']}]" if "default" in arg else ""
                    choices = f" [choices: {', '.join(arg['choices'])}]" if "choices" in arg else ""
                    help_text += f"  {arg['name']}: {arg.get('description', 'No description')}{required}{default}{choices}\n"
            
            return help_text
        else:
            # List all commands
            help_text = "Available slash commands:\n\n"
            for name, cmd in sorted(self.commands.items()):
                aliases = f" ({', '.join(cmd.aliases)})" if cmd.aliases else ""
                help_text += f"  /{name}{aliases} - {cmd.description}\n"
            
            help_text += "\nUse /help <command> for detailed help on a specific command."
            return help_text

    # Command handlers
    async def _cmd_help(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /help command."""
        command_name = args[0] if args else kwargs.get("command")
        return self.get_command_help(command_name)

    async def _cmd_clear(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /clear command."""
        if self.terminal_app and hasattr(self.terminal_app, 'terminal_chat'):
            await self.terminal_app.terminal_chat.clear()
            return "Chat history cleared."
        return "Unable to clear chat history."

    async def _cmd_compact(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /compact command."""
        if self.session_manager:
            # Get current session and compress its history
            current_session = await self.session_manager.get_current_session()
            if current_session:
                # This would implement conversation summarization
                return "Conversation history compacted to save context."
        return "Unable to compact conversation history."

    async def _cmd_history(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /history command."""
        limit = int(kwargs.get("limit", args[0] if args else 10))
        search = kwargs.get("search", args[1] if len(args) > 1 else None)

        if self.session_manager:
            current_session = await self.session_manager.get_current_session()
            if current_session:
                messages = await self.session_manager.get_messages(current_session.id, limit=limit)

                if search:
                    messages = [msg for msg in messages if search.lower() in msg.content.lower()]

                if not messages:
                    return "No messages found."

                history_text = f"Last {len(messages)} messages:\n\n"
                for msg in messages[-limit:]:
                    timestamp = msg.timestamp.strftime("%H:%M:%S")
                    role = msg.role.title()
                    content = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
                    history_text += f"[{timestamp}] {role}: {content}\n"

                return history_text

        return "Unable to retrieve conversation history."

    async def _cmd_sessions(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /sessions command."""
        action = kwargs.get("action", args[0] if args else "list")
        name = kwargs.get("name", args[1] if len(args) > 1 else None)

        if not self.session_manager:
            return "Session manager not available."

        if action == "list":
            sessions = await self.session_manager.list_sessions()
            if not sessions:
                return "No sessions found."

            current = await self.session_manager.get_current_session()
            current_id = current.id if current else None

            session_text = "Available sessions:\n\n"
            for session in sessions:
                marker = "* " if session.id == current_id else "  "
                last_accessed = session.last_accessed.strftime("%Y-%m-%d %H:%M")
                session_text += f"{marker}{session.name} (last accessed: {last_accessed})\n"

            return session_text

        elif action == "new":
            if not name:
                name = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            try:
                session = await self.session_manager.create_session(name)
                return f"Created new session: {session.name}"
            except Exception as e:
                return f"Failed to create session: {e}"

        elif action == "load":
            if not name:
                return "Session name required for load action."

            try:
                await self.session_manager.load_session(name)
                return f"Loaded session: {name}"
            except Exception as e:
                return f"Failed to load session: {e}"

        elif action == "delete":
            if not name:
                return "Session name required for delete action."

            try:
                await self.session_manager.delete_session(name)
                return f"Deleted session: {name}"
            except Exception as e:
                return f"Failed to delete session: {e}"

        elif action == "rename":
            return "Session rename not yet implemented."

        else:
            return f"Unknown session action: {action}"

    async def _cmd_model(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /model command."""
        model = kwargs.get("model", args[0] if args else None)
        provider = kwargs.get("provider", args[1] if len(args) > 1 else None)

        if not model and not provider:
            # Show current model/provider
            current_model = self.config.get("ai.default_model", "Unknown")
            current_provider = self.config.get("ai.default_provider", "Unknown")
            return f"Current model: {current_provider}/{current_model}"

        if provider:
            self.config.set("ai.default_provider", provider)
            if self.agent_engine:
                self.agent_engine.current_provider = provider

        if model:
            self.config.set("ai.default_model", model)
            if self.agent_engine:
                self.agent_engine.current_model = model

        return f"Switched to {provider or 'current provider'}/{model or 'current model'}"

    async def _cmd_diff(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /diff command."""
        if not args:
            return "File path required for diff command."

        file1 = args[0]
        file2 = args[1] if len(args) > 1 else None

        # This would integrate with the git tool or file operations
        return f"Diff functionality for {file1} vs {file2 or 'working copy'} not yet implemented."

    async def _cmd_config(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /config command."""
        key = kwargs.get("key", args[0] if args else None)
        value = kwargs.get("value", args[1] if len(args) > 1 else None)

        if not key:
            # Show all configuration
            config_text = "Current configuration:\n\n"
            for section, values in self.config._config.items():
                config_text += f"[{section}]\n"
                if isinstance(values, dict):
                    for k, v in values.items():
                        config_text += f"  {k} = {v}\n"
                else:
                    config_text += f"  {values}\n"
                config_text += "\n"
            return config_text

        if value is None:
            # Get configuration value
            try:
                current_value = self.config.get(key)
                return f"{key} = {current_value}"
            except Exception as e:
                return f"Failed to get configuration: {e}"
        else:
            # Set configuration value
            try:
                self.config.set(key, value)
                return f"Set {key} = {value}"
            except Exception as e:
                return f"Failed to set configuration: {e}"

    async def _cmd_export(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /export command."""
        format_type = kwargs.get("format", args[0] if args else "json")
        file_path = kwargs.get("file", args[1] if len(args) > 1 else None)
        session_name = kwargs.get("session", args[2] if len(args) > 2 else None)

        if not self.session_manager:
            return "Session manager not available."

        try:
            # Get session to export
            if session_name:
                session = await self.session_manager.get_session_by_name(session_name)
                if not session:
                    return f"Session '{session_name}' not found."
            else:
                session = await self.session_manager.get_current_session()
                if not session:
                    return "No current session to export."

            # Export session data
            export_data = await self.session_manager.export_session(session.id)

            # Format data
            if format_type == "json":
                content = json.dumps(export_data, indent=2, default=str)
                extension = ".json"
            elif format_type == "markdown":
                content = self._format_as_markdown(export_data)
                extension = ".md"
            elif format_type == "text":
                content = self._format_as_text(export_data)
                extension = ".txt"
            else:
                return f"Unknown export format: {format_type}"

            # Save to file
            if not file_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_path = f"session_export_{timestamp}{extension}"

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            return f"Session exported to {file_path}"

        except Exception as e:
            return f"Failed to export session: {e}"

    async def _cmd_import(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /import command."""
        if not args:
            return "File path required for import command."

        file_path = args[0]
        format_type = kwargs.get("format", args[1] if len(args) > 1 else "json")

        if not self.session_manager:
            return "Session manager not available."

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if format_type == "json":
                import_data = json.loads(content)
            else:
                return f"Import format '{format_type}' not yet supported."

            # Import session
            session = await self.session_manager.import_session(import_data)
            return f"Session '{session.name}' imported successfully."

        except FileNotFoundError:
            return f"File not found: {file_path}"
        except json.JSONDecodeError as e:
            return f"Invalid JSON format: {e}"
        except Exception as e:
            return f"Failed to import session: {e}"

    async def _cmd_status(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /status command."""
        status_text = "AI Terminal Status:\n\n"

        # System information
        if self.agent_engine:
            status_text += f"Current Model: {self.agent_engine.current_provider}/{self.agent_engine.current_model}\n"
            status_text += f"Processing: {'Yes' if self.agent_engine.is_processing else 'No'}\n"

        # Session information
        if self.session_manager:
            current_session = await self.session_manager.get_current_session()
            if current_session:
                status_text += f"Current Session: {current_session.name}\n"
                status_text += f"Session Created: {current_session.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"

                # Message count
                messages = await self.session_manager.get_messages(current_session.id)
                status_text += f"Messages in Session: {len(messages)}\n"

        # Configuration
        status_text += f"\nConfiguration Directory: {self.config.config_dir}\n"
        status_text += f"Theme: {self.config.get('ui.theme', 'dark')}\n"
        status_text += f"Auto-save: {self.config.get('session.auto_save', True)}\n"

        return status_text

    async def _cmd_reset(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /reset command."""
        if self.session_manager:
            current_session = await self.session_manager.get_current_session()
            if current_session:
                # Clear current session messages
                await self.session_manager.clear_session_messages(current_session.id)

                # Clear chat display
                if self.terminal_app and hasattr(self.terminal_app, 'terminal_chat'):
                    await self.terminal_app.terminal_chat.clear()

                return f"Session '{current_session.name}' has been reset."

        return "Unable to reset session."

    async def _cmd_quit(self, args: List[str], kwargs: Dict[str, Any]) -> str:
        """Handle /quit command."""
        if self.terminal_app:
            await self.terminal_app.action_quit()
        return "Goodbye!"

    def _format_as_markdown(self, export_data: Dict[str, Any]) -> str:
        """Format export data as markdown."""
        session = export_data["session"]
        messages = export_data.get("messages", [])

        content = f"# {session['name']}\n\n"
        content += f"**Created:** {session['created_at']}\n"
        content += f"**Model:** {session.get('provider', 'Unknown')}/{session.get('model', 'Unknown')}\n\n"

        if session.get('description'):
            content += f"**Description:** {session['description']}\n\n"

        content += "## Conversation\n\n"

        for msg in messages:
            role = msg['role'].title()
            timestamp = msg['timestamp']
            content += f"### {role} ({timestamp})\n\n"
            content += f"{msg['content']}\n\n"

            if msg.get('tool_calls'):
                content += "**Tool Calls:**\n"
                for tool_call in msg['tool_calls']:
                    content += f"- {tool_call['name']}: {tool_call.get('result', 'No result')}\n"
                content += "\n"

        return content

    def _format_as_text(self, export_data: Dict[str, Any]) -> str:
        """Format export data as plain text."""
        session = export_data["session"]
        messages = export_data.get("messages", [])

        content = f"{session['name']}\n"
        content += "=" * len(session['name']) + "\n\n"
        content += f"Created: {session['created_at']}\n"
        content += f"Model: {session.get('provider', 'Unknown')}/{session.get('model', 'Unknown')}\n\n"

        if session.get('description'):
            content += f"Description: {session['description']}\n\n"

        content += "Conversation:\n\n"

        for msg in messages:
            role = msg['role'].upper()
            timestamp = msg['timestamp']
            content += f"[{timestamp}] {role}:\n"
            content += f"{msg['content']}\n\n"

            if msg.get('tool_calls'):
                content += "Tool Calls:\n"
                for tool_call in msg['tool_calls']:
                    content += f"  - {tool_call['name']}: {tool_call.get('result', 'No result')}\n"
                content += "\n"

        return content
