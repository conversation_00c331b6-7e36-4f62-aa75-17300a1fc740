"""
Test suite for slash command system.

Tests the comprehensive slash command processor and all command handlers
to ensure proper parsing, validation, and execution.
"""

import asyncio
import json
import tempfile
from datetime import datetime
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from ai_terminal.core.config import Config
from ai_terminal.storage.models import Message, Session
from ai_terminal.ui.commands import S<PERSON>Command, SlashCommandProcessor


@pytest.fixture
def mock_config():
    """Create a mock configuration."""
    config = MagicMock(spec=Config)
    config.get.return_value = "test_value"
    config.set.return_value = None
    config.config_dir = Path(tempfile.mkdtemp())
    config._config = {
        "ai": {"default_model": "test-model", "default_provider": "test-provider"},
        "ui": {"theme": "dark"},
        "session": {"auto_save": True}
    }
    return config


@pytest.fixture
def mock_session_manager():
    """Create a mock session manager."""
    session_manager = AsyncMock()
    
    # Mock session
    mock_session = Session(
        id="test-session-id",
        name="test-session",
        description="Test session",
        created_at=datetime.now(),
        last_accessed=datetime.now(),
        provider="test-provider",
        model="test-model"
    )
    
    # Mock messages
    mock_messages = [
        Message(
            id="msg-1",
            session_id="test-session-id",
            role="user",
            content="Hello",
            timestamp=datetime.now()
        ),
        Message(
            id="msg-2",
            session_id="test-session-id",
            role="assistant",
            content="Hi there!",
            timestamp=datetime.now()
        )
    ]
    
    session_manager.get_current_session.return_value = mock_session
    session_manager.list_sessions.return_value = [mock_session]
    session_manager.get_messages.return_value = mock_messages
    session_manager.create_session.return_value = mock_session
    session_manager.delete_session.return_value = None
    session_manager.export_session.return_value = {
        "session": {
            "id": mock_session.id,
            "name": mock_session.name,
            "created_at": mock_session.created_at.isoformat()
        },
        "messages": [
            {
                "id": msg.id,
                "role": msg.role,
                "content": msg.content,
                "timestamp": msg.timestamp.isoformat()
            }
            for msg in mock_messages
        ]
    }
    
    return session_manager


@pytest.fixture
def mock_agent_engine():
    """Create a mock agent engine."""
    agent_engine = AsyncMock()
    agent_engine.current_provider = "test-provider"
    agent_engine.current_model = "test-model"
    agent_engine.is_processing = False
    return agent_engine


@pytest.fixture
def mock_terminal_app():
    """Create a mock terminal app."""
    terminal_app = AsyncMock()
    terminal_app.terminal_chat = AsyncMock()
    terminal_app.terminal_chat.clear = AsyncMock()
    terminal_app.action_quit = AsyncMock()
    return terminal_app


@pytest.fixture
def slash_processor(mock_config, mock_session_manager, mock_agent_engine, mock_terminal_app):
    """Create a slash command processor with mocked dependencies."""
    return SlashCommandProcessor(
        config=mock_config,
        session_manager=mock_session_manager,
        agent_engine=mock_agent_engine,
        terminal_app=mock_terminal_app
    )


class TestSlashCommandProcessor:
    """Test the slash command processor."""
    
    def test_is_slash_command(self, slash_processor):
        """Test slash command detection."""
        assert slash_processor.is_slash_command("/help")
        assert slash_processor.is_slash_command("  /clear  ")
        assert not slash_processor.is_slash_command("help")
        assert not slash_processor.is_slash_command("regular text")
    
    def test_parse_command_basic(self, slash_processor):
        """Test basic command parsing."""
        cmd, args, kwargs = slash_processor.parse_command("/help")
        assert cmd == "help"
        assert args == []
        assert kwargs == {}
    
    def test_parse_command_with_args(self, slash_processor):
        """Test command parsing with arguments."""
        cmd, args, kwargs = slash_processor.parse_command("/history 20 search_term")
        assert cmd == "history"
        assert args == ["20", "search_term"]
        assert kwargs == {}
    
    def test_parse_command_with_kwargs(self, slash_processor):
        """Test command parsing with keyword arguments."""
        cmd, args, kwargs = slash_processor.parse_command("/export --format json --file output.json")
        assert cmd == "export"
        assert args == []
        assert kwargs == {"format": "json", "file": "output.json"}
    
    def test_parse_command_mixed_args(self, slash_processor):
        """Test command parsing with mixed arguments."""
        cmd, args, kwargs = slash_processor.parse_command("/sessions new --name test_session")
        assert cmd == "sessions"
        assert args == ["new"]
        assert kwargs == {"name": "test_session"}
    
    def test_parse_command_aliases(self, slash_processor):
        """Test command alias resolution."""
        cmd, args, kwargs = slash_processor.parse_command("/h")
        assert cmd == "help"
        
        cmd, args, kwargs = slash_processor.parse_command("/cls")
        assert cmd == "clear"
    
    def test_parse_command_quoted_args(self, slash_processor):
        """Test parsing quoted arguments."""
        cmd, args, kwargs = slash_processor.parse_command('/sessions new "my session name"')
        assert cmd == "sessions"
        assert args == ["new", "my session name"]
    
    @pytest.mark.asyncio
    async def test_help_command(self, slash_processor):
        """Test help command execution."""
        result = await slash_processor.execute_command("/help")
        assert result["success"] is True
        assert "Available slash commands" in result["result"]
    
    @pytest.mark.asyncio
    async def test_help_command_specific(self, slash_processor):
        """Test help for specific command."""
        result = await slash_processor.execute_command("/help clear")
        assert result["success"] is True
        assert "clear" in result["result"].lower()
    
    @pytest.mark.asyncio
    async def test_clear_command(self, slash_processor, mock_terminal_app):
        """Test clear command execution."""
        result = await slash_processor.execute_command("/clear --force")
        assert result["success"] is True
        mock_terminal_app.terminal_chat.clear.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_clear_command_requires_confirmation(self, slash_processor):
        """Test clear command requires confirmation."""
        result = await slash_processor.execute_command("/clear")
        assert result["success"] is False
        assert result["requires_confirmation"] is True
    
    @pytest.mark.asyncio
    async def test_history_command(self, slash_processor, mock_session_manager):
        """Test history command execution."""
        result = await slash_processor.execute_command("/history")
        assert result["success"] is True
        assert "messages" in result["result"].lower()
        mock_session_manager.get_current_session.assert_called_once()
        mock_session_manager.get_messages.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_sessions_list_command(self, slash_processor, mock_session_manager):
        """Test sessions list command."""
        result = await slash_processor.execute_command("/sessions list")
        assert result["success"] is True
        assert "Available sessions" in result["result"]
        mock_session_manager.list_sessions.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_sessions_new_command(self, slash_processor, mock_session_manager):
        """Test sessions new command."""
        result = await slash_processor.execute_command("/sessions new test_session")
        assert result["success"] is True
        mock_session_manager.create_session.assert_called_once_with("test_session")
    
    @pytest.mark.asyncio
    async def test_model_command_show_current(self, slash_processor, mock_config):
        """Test model command showing current model."""
        result = await slash_processor.execute_command("/model")
        assert result["success"] is True
        assert "Current model" in result["result"]
    
    @pytest.mark.asyncio
    async def test_model_command_switch(self, slash_processor, mock_config, mock_agent_engine):
        """Test model command switching model."""
        result = await slash_processor.execute_command("/model --model new-model --provider new-provider")
        assert result["success"] is True
        mock_config.set.assert_called()
        assert mock_agent_engine.current_model == "new-model"
        assert mock_agent_engine.current_provider == "new-provider"
    
    @pytest.mark.asyncio
    async def test_config_command_show_all(self, slash_processor, mock_config):
        """Test config command showing all configuration."""
        result = await slash_processor.execute_command("/config")
        assert result["success"] is True
        assert "Current configuration" in result["result"]
    
    @pytest.mark.asyncio
    async def test_config_command_get_value(self, slash_processor, mock_config):
        """Test config command getting specific value."""
        result = await slash_processor.execute_command("/config ai.default_model")
        assert result["success"] is True
        mock_config.get.assert_called_with("ai.default_model")
    
    @pytest.mark.asyncio
    async def test_config_command_set_value(self, slash_processor, mock_config):
        """Test config command setting value."""
        result = await slash_processor.execute_command("/config ai.default_model new-model")
        assert result["success"] is True
        mock_config.set.assert_called_with("ai.default_model", "new-model")
    
    @pytest.mark.asyncio
    async def test_export_command(self, slash_processor, mock_session_manager):
        """Test export command."""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            temp_path = f.name
        
        try:
            result = await slash_processor.execute_command(f"/export --file {temp_path}")
            assert result["success"] is True
            assert "exported" in result["result"].lower()
            
            # Verify file was created and contains valid JSON
            with open(temp_path, 'r') as f:
                data = json.load(f)
                assert "session" in data
                assert "messages" in data
        finally:
            Path(temp_path).unlink(missing_ok=True)
    
    @pytest.mark.asyncio
    async def test_status_command(self, slash_processor, mock_agent_engine, mock_session_manager):
        """Test status command."""
        result = await slash_processor.execute_command("/status")
        assert result["success"] is True
        assert "AI Terminal Status" in result["result"]
        assert "Current Model" in result["result"]
    
    @pytest.mark.asyncio
    async def test_unknown_command(self, slash_processor):
        """Test unknown command handling."""
        result = await slash_processor.execute_command("/unknown_command")
        assert result["success"] is False
        assert "Unknown command" in result["error"]
    
    @pytest.mark.asyncio
    async def test_quit_command(self, slash_processor, mock_terminal_app):
        """Test quit command."""
        result = await slash_processor.execute_command("/quit --force")
        assert result["success"] is True
        mock_terminal_app.action_quit.assert_called_once()
    
    def test_command_registration(self, slash_processor):
        """Test that all expected commands are registered."""
        expected_commands = [
            "help", "clear", "compact", "history", "sessions", "model",
            "diff", "config", "export", "import", "status", "reset", "quit"
        ]
        
        for cmd in expected_commands:
            assert cmd in slash_processor.commands
        
        # Test aliases
        assert "h" in slash_processor.aliases
        assert "cls" in slash_processor.aliases
        assert slash_processor.aliases["h"] == "help"
        assert slash_processor.aliases["cls"] == "clear"
    
    def test_get_command_help(self, slash_processor):
        """Test command help generation."""
        # Test general help
        help_text = slash_processor.get_command_help()
        assert "Available slash commands" in help_text
        assert "/help" in help_text
        
        # Test specific command help
        help_text = slash_processor.get_command_help("help")
        assert "/help" in help_text
        assert "Show help information" in help_text
        
        # Test unknown command
        help_text = slash_processor.get_command_help("unknown")
        assert "Unknown command" in help_text
