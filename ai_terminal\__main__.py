"""
Main entry point for AI Terminal.

Handles command-line arguments, initialization, and application startup
with proper error handling and logging configuration.
"""

import argparse
import asyncio
import sys
from pathlib import Path

from ai_terminal.core.app import AITerminalApp
from ai_terminal.core.config import Config
from ai_terminal.core.logger import setup_logging
from ai_terminal.ui.onboarding import OnboardingWizard


def parse_arguments() -> argparse.Namespace:
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        prog="ai-terminal",
        description="Autonomous AI-powered CLI terminal application",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  ai-terminal                    # Start with default configuration
  ai-terminal --debug           # Start with debug logging
  ai-terminal --config-dir ~/my-config  # Use custom config directory
  ai-terminal --setup           # Run setup wizard
        """
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="AI Terminal 1.0.0"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug logging"
    )
    
    parser.add_argument(
        "--config-dir",
        type=Path,
        help="Custom configuration directory"
    )
    
    parser.add_argument(
        "--setup",
        action="store_true",
        help="Run the setup wizard"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default="INFO",
        help="Set logging level"
    )
    
    parser.add_argument(
        "--no-ui",
        action="store_true",
        help="Run without terminal UI (for testing)"
    )
    
    return parser.parse_args()


async def run_setup_wizard(config: Config) -> bool:
    """Run the onboarding setup wizard."""
    try:
        wizard = OnboardingWizard(config)
        return await wizard.run()
    except KeyboardInterrupt:
        print("\nSetup cancelled by user.")
        return False
    except Exception as e:
        print(f"Setup failed: {e}")
        return False


async def main() -> int:
    """Main application entry point."""
    args = parse_arguments()
    
    # Setup logging
    log_level = "DEBUG" if args.debug else args.log_level
    setup_logging(level=log_level)
    
    try:
        # Initialize configuration
        config = Config(config_dir=args.config_dir)
        
        # Run setup wizard if requested or not configured
        if args.setup or not config.is_configured():
            print("🤖 Welcome to AI Terminal!")
            print("Let's get you set up with your AI assistant.\n")
            
            success = await run_setup_wizard(config)
            if not success:
                print("Setup incomplete. Please run 'ai-terminal --setup' to try again.")
                return 1
            
            print("\n✅ Setup complete! Starting AI Terminal...\n")
        
        # Create and run the application
        app = AITerminalApp(config)
        
        if args.no_ui:
            # For testing - just initialize and exit
            await app.initialize()
            print("AI Terminal initialized successfully (no UI mode)")
            await app.shutdown()
            return 0
        else:
            # Run the full application
            await app.run()
            return 0
    
    except KeyboardInterrupt:
        print("\nGoodbye! 👋")
        return 0
    
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        return 1


def cli_main() -> None:
    """CLI entry point for setuptools."""
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nGoodbye! 👋")
        sys.exit(0)


if __name__ == "__main__":
    cli_main()
