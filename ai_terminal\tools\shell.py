"""
Shell command execution tool for AI Terminal.

Provides secure shell command execution with safety checks,
output capture, and timeout handling.
"""

import asyncio
import os
import shlex
import subprocess
from typing import Any, Dict

from ai_terminal.tools.base import BaseTool, ToolExecutionError, ToolTimeoutError


class ShellTool(BaseTool):
    """
    Shell command execution tool.
    
    Executes shell commands with security checks and output capture.
    Supports both synchronous and asynchronous execution.
    """
    
    def __init__(self):
        """Initialize shell tool."""
        super().__init__(
            name="shell",
            description="Execute shell commands safely with output capture"
        )
        self.requires_approval = True
        self.timeout = 30
        
        # Dangerous commands that always require approval
        self.dangerous_commands = {
            "rm", "del", "format", "fdisk", "dd", "mkfs", 
            "shutdown", "reboot", "halt", "poweroff",
            "sudo rm", "sudo del", "sudo format",
            "chmod 777", "chown", "passwd", "su",
        }
    
    async def execute(self, command: str, working_dir: str = None, **kwargs) -> Dict[str, Any]:
        """
        Execute a shell command.
        
        Args:
            command: Shell command to execute
            working_dir: Working directory for command execution
            **kwargs: Additional options
            
        Returns:
            Dictionary with execution results
        """
        try:
            # Validate command
            if not command or not command.strip():
                raise ToolExecutionError("Command cannot be empty", self.name)
            
            # Set working directory
            cwd = working_dir or os.getcwd()
            if not os.path.exists(cwd):
                raise ToolExecutionError(f"Working directory does not exist: {cwd}", self.name)
            
            # Log command execution
            self.logger.info(f"Executing command: {command}")
            self.logger.debug(f"Working directory: {cwd}")
            
            # Execute command
            if os.name == 'nt':  # Windows
                result = await self._execute_windows(command, cwd)
            else:  # Unix-like
                result = await self._execute_unix(command, cwd)
            
            return result
            
        except asyncio.TimeoutError:
            raise ToolTimeoutError(f"Command timed out after {self.timeout}s", self.name)
        except Exception as e:
            self.logger.error(f"Shell command execution failed: {e}")
            raise ToolExecutionError(f"Command execution failed: {e}", self.name)
    
    async def _execute_windows(self, command: str, cwd: str) -> Dict[str, Any]:
        """Execute command on Windows."""
        try:
            # Use PowerShell for better Unicode support
            ps_command = f"powershell.exe -Command \"{command}\""
            
            process = await asyncio.create_subprocess_shell(
                ps_command,
                cwd=cwd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                shell=True
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=self.timeout
            )
            
            return {
                "command": command,
                "exit_code": process.returncode,
                "stdout": stdout.decode('utf-8', errors='replace').strip(),
                "stderr": stderr.decode('utf-8', errors='replace').strip(),
                "working_dir": cwd,
                "success": process.returncode == 0,
            }
            
        except Exception as e:
            raise ToolExecutionError(f"Windows command execution failed: {e}", self.name)
    
    async def _execute_unix(self, command: str, cwd: str) -> Dict[str, Any]:
        """Execute command on Unix-like systems."""
        try:
            process = await asyncio.create_subprocess_shell(
                command,
                cwd=cwd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                shell=True
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=self.timeout
            )
            
            return {
                "command": command,
                "exit_code": process.returncode,
                "stdout": stdout.decode('utf-8', errors='replace').strip(),
                "stderr": stderr.decode('utf-8', errors='replace').strip(),
                "working_dir": cwd,
                "success": process.returncode == 0,
            }
            
        except Exception as e:
            raise ToolExecutionError(f"Unix command execution failed: {e}", self.name)
    
    def is_dangerous(self, args: Dict[str, Any]) -> bool:
        """Check if command is potentially dangerous."""
        command = args.get("command", "").lower().strip()
        
        # Check against dangerous command list
        for dangerous_cmd in self.dangerous_commands:
            if dangerous_cmd in command:
                return True
        
        # Check for other dangerous patterns
        dangerous_patterns = [
            ">/dev/", ">/proc/", ">/sys/",  # Writing to system directories
            "curl", "wget", "nc ", "netcat",  # Network operations
            "python -c", "perl -e", "ruby -e",  # Code execution
            "eval", "exec",  # Dynamic execution
            "find / -", "find . -delete",  # Potentially destructive find operations
        ]
        
        for pattern in dangerous_patterns:
            if pattern in command:
                return True
        
        return False
    
    def get_schema(self) -> Dict[str, Any]:
        """Get JSON schema for shell tool."""
        return {
            "name": "shell",
            "description": "Execute shell commands safely with output capture",
            "parameters": {
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "Shell command to execute"
                    },
                    "working_dir": {
                        "type": "string",
                        "description": "Working directory for command execution (optional)"
                    }
                },
                "required": ["command"]
            }
        }
    
    async def validate_command(self, command: str) -> Dict[str, Any]:
        """Validate a command before execution."""
        validation_result = {
            "valid": True,
            "warnings": [],
            "errors": [],
            "requires_approval": False
        }
        
        if not command or not command.strip():
            validation_result["valid"] = False
            validation_result["errors"].append("Command cannot be empty")
            return validation_result
        
        # Check for dangerous commands
        if self.is_dangerous({"command": command}):
            validation_result["requires_approval"] = True
            validation_result["warnings"].append("Command may be dangerous and requires approval")
        
        # Check command length
        if len(command) > 1000:
            validation_result["warnings"].append("Command is very long")
        
        # Check for command injection patterns
        injection_patterns = [";", "&&", "||", "|", "`", "$(", "${"]
        for pattern in injection_patterns:
            if pattern in command:
                validation_result["warnings"].append(f"Command contains potentially dangerous pattern: {pattern}")
        
        return validation_result
    
    async def get_command_help(self, command: str) -> str:
        """Get help for a specific command."""
        try:
            if os.name == 'nt':  # Windows
                help_command = f"Get-Help {command}"
                result = await self.execute(help_command)
            else:  # Unix-like
                help_command = f"man {command} 2>/dev/null || {command} --help 2>/dev/null || which {command}"
                result = await self.execute(help_command)
            
            if result["success"]:
                return result["stdout"]
            else:
                return f"No help available for command: {command}"
                
        except Exception as e:
            return f"Error getting help for {command}: {e}"
    
    async def which(self, command: str) -> str:
        """Find the path of a command."""
        try:
            if os.name == 'nt':  # Windows
                result = await self.execute(f"where {command}")
            else:  # Unix-like
                result = await self.execute(f"which {command}")
            
            if result["success"]:
                return result["stdout"].split('\n')[0].strip()
            else:
                return ""
                
        except Exception:
            return ""
