#!/usr/bin/env python3
"""Setup a test configuration to bypass the onboarding wizard."""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'ai_terminal'))

from ai_terminal.core.config import Config, AIProviderConfig

def setup_test_config():
    """Setup a minimal test configuration."""
    print("Setting up test configuration...")
    
    # Create config
    config = Config()
    
    # Create a test provider configuration
    provider_config = AIProviderConfig(
        name="deepseek",
        api_key_service="ai-terminal-deepseek",
        api_key_username="api-key",
        base_url="https://api.deepseek.com",
        model="deepseek-chat",
        enabled=True
    )
    
    # Add provider to config
    config.add_provider(provider_config)
    
    # Set a dummy API key for testing
    config.set_api_key("deepseek", "test-api-key-for-testing")
    
    # Set default provider and model
    config.set("ai.default_provider", "deepseek")
    config.set("ai.default_model", "deepseek-chat")
    
    # Verify configuration
    if config.is_configured():
        print("✅ Test configuration created successfully!")
        print(f"Config directory: {config.config_dir}")
        print(f"Default provider: {config.get('ai.default_provider')}")
        print(f"Default model: {config.get('ai.default_model')}")
        return True
    else:
        print("❌ Configuration setup failed!")
        return False

if __name__ == "__main__":
    success = setup_test_config()
    sys.exit(0 if success else 1)
