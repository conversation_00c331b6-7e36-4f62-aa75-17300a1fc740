# Contributing to AI Terminal

Thank you for your interest in contributing to AI Terminal! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/ai-terminal.git
   cd ai-terminal
   ```

2. **Create Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Development Dependencies**
   ```bash
   pip install -e ".[dev]"
   ```

4. **Run Tests**
   ```bash
   pytest
   ```

### Development Workflow

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Follow the coding standards below
   - Add tests for new functionality
   - Update documentation as needed

3. **Run Quality Checks**
   ```bash
   # Run tests
   pytest

   # Check code formatting
   black --check ai_terminal/

   # Check imports
   isort --check-only ai_terminal/

   # Type checking
   mypy ai_terminal/

   # Linting
   flake8 ai_terminal/
   ```

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## 📝 Coding Standards

### Python Style Guide

- **PEP 8**: Follow Python PEP 8 style guide
- **Black**: Use Black for code formatting
- **isort**: Use isort for import sorting
- **Type Hints**: Add type hints to all functions and methods
- **Docstrings**: Use Google-style docstrings

### Code Quality

- **Line Length**: Maximum 88 characters (Black default)
- **Imports**: Group imports (standard library, third-party, local)
- **Naming**: Use descriptive names for variables and functions
- **Comments**: Write clear, concise comments for complex logic

### Example Code Style

```python
"""
Module docstring describing the purpose.

This module provides functionality for...
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional

from third_party_library import SomeClass

from ai_terminal.core.logger import LoggerMixin


class ExampleClass(LoggerMixin):
    """
    Example class demonstrating coding standards.
    
    This class shows how to structure code according to
    the project's coding standards and conventions.
    """
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """
        Initialize the example class.
        
        Args:
            name: The name of the instance
            config: Configuration dictionary
        """
        self.name = name
        self.config = config
        self.logger.info(f"Initialized {name}")
    
    async def process_data(
        self, 
        data: List[str], 
        options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process data asynchronously.
        
        Args:
            data: List of data items to process
            options: Optional processing options
            
        Returns:
            Dictionary containing processing results
            
        Raises:
            ValueError: If data is empty
        """
        if not data:
            raise ValueError("Data cannot be empty")
        
        options = options or {}
        results = {"processed": len(data), "items": []}
        
        for item in data:
            # Process each item
            processed_item = await self._process_item(item, options)
            results["items"].append(processed_item)
        
        return results
    
    async def _process_item(self, item: str, options: Dict[str, Any]) -> str:
        """Process a single item (private method)."""
        # Implementation details
        return item.upper()
```

## 🧪 Testing Guidelines

### Test Structure

- **Unit Tests**: Test individual functions and methods
- **Integration Tests**: Test component interactions
- **End-to-End Tests**: Test complete workflows
- **Mock External Dependencies**: Use mocks for external services

### Test Organization

```
tests/
├── unit/
│   ├── test_core/
│   ├── test_agents/
│   ├── test_tools/
│   └── test_ui/
├── integration/
│   ├── test_agent_tool_integration.py
│   └── test_provider_integration.py
└── e2e/
    └── test_complete_workflows.py
```

### Writing Tests

```python
import pytest
from unittest.mock import AsyncMock, Mock, patch

from ai_terminal.tools.shell import ShellTool


class TestShellTool:
    """Test cases for ShellTool."""
    
    @pytest.fixture
    def shell_tool(self):
        """Create a ShellTool instance for testing."""
        return ShellTool()
    
    @pytest.mark.asyncio
    async def test_execute_simple_command(self, shell_tool):
        """Test executing a simple shell command."""
        result = await shell_tool.execute(command="echo 'hello'")
        
        assert result["success"] is True
        assert "hello" in result["stdout"]
        assert result["exit_code"] == 0
    
    @pytest.mark.asyncio
    async def test_execute_dangerous_command_requires_approval(self, shell_tool):
        """Test that dangerous commands require approval."""
        is_dangerous = shell_tool.is_dangerous({"command": "rm -rf /"})
        assert is_dangerous is True
    
    @pytest.mark.asyncio
    async def test_execute_with_timeout(self, shell_tool):
        """Test command execution with timeout."""
        shell_tool.timeout = 1
        
        with pytest.raises(ToolTimeoutError):
            await shell_tool.execute(command="sleep 5")
```

## 📚 Documentation

### Code Documentation

- **Docstrings**: All public functions, classes, and methods must have docstrings
- **Type Hints**: Use comprehensive type hints
- **Comments**: Explain complex logic and business rules
- **README Updates**: Update README.md for new features

### Documentation Style

```python
def complex_function(
    param1: str, 
    param2: Optional[List[Dict[str, Any]]] = None
) -> Tuple[bool, str]:
    """
    Brief description of what the function does.
    
    Longer description explaining the purpose, behavior,
    and any important details about the function.
    
    Args:
        param1: Description of the first parameter
        param2: Description of the optional second parameter.
            Can be None, in which case default behavior applies.
    
    Returns:
        Tuple containing:
        - bool: Success status
        - str: Result message or error description
    
    Raises:
        ValueError: If param1 is empty
        RuntimeError: If operation fails
    
    Example:
        >>> success, message = complex_function("test", [{"key": "value"}])
        >>> print(f"Success: {success}, Message: {message}")
    """
```

## 🔧 Adding New Features

### New Tools

1. **Create Tool Class**
   ```python
   # ai_terminal/tools/my_tool.py
   from ai_terminal.tools.base import BaseTool
   
   class MyTool(BaseTool):
       def __init__(self):
           super().__init__(
               name="my_tool",
               description="Description of what the tool does"
           )
       
       async def execute(self, **kwargs):
           # Implementation
           pass
       
       def get_schema(self):
           # JSON schema for the tool
           pass
   ```

2. **Register Tool**
   ```python
   # ai_terminal/tools/manager.py
   from ai_terminal.tools.my_tool import MyTool
   
   TOOL_CLASSES = {
       # ... existing tools
       "my_tool": MyTool,
   }
   ```

3. **Add Tests**
   ```python
   # tests/unit/test_tools/test_my_tool.py
   class TestMyTool:
       # Test cases
       pass
   ```

### New AI Providers

1. **Create Provider Class**
   ```python
   # ai_terminal/providers/my_provider.py
   from ai_terminal.providers.base import BaseProvider
   
   class MyProvider(BaseProvider):
       # Implementation
       pass
   ```

2. **Register Provider**
   ```python
   # ai_terminal/providers/manager.py
   PROVIDER_CLASSES = {
       # ... existing providers
       "my_provider": MyProvider,
   }
   ```

### New UI Components

1. **Create Component**
   ```python
   # ai_terminal/ui/components/my_component.py
   from textual.widgets import Static
   
   class MyComponent(Static):
       # Implementation
       pass
   ```

2. **Add to UI**
   ```python
   # ai_terminal/ui/terminal.py
   # Import and use the component
   ```

## 🐛 Bug Reports

### Bug Report Template

```markdown
**Bug Description**
A clear description of the bug.

**Steps to Reproduce**
1. Step one
2. Step two
3. Step three

**Expected Behavior**
What you expected to happen.

**Actual Behavior**
What actually happened.

**Environment**
- OS: [e.g., Windows 11, macOS 13, Ubuntu 22.04]
- Python Version: [e.g., 3.11.0]
- AI Terminal Version: [e.g., 1.0.0]
- AI Provider: [e.g., DeepSeek, OpenAI]

**Additional Context**
Any other relevant information.
```

## 💡 Feature Requests

### Feature Request Template

```markdown
**Feature Description**
A clear description of the feature you'd like to see.

**Use Case**
Describe the problem this feature would solve.

**Proposed Solution**
Your ideas for how this could be implemented.

**Alternatives Considered**
Other approaches you've considered.

**Additional Context**
Any other relevant information.
```

## 📋 Pull Request Guidelines

### PR Checklist

- [ ] Code follows project style guidelines
- [ ] Tests added for new functionality
- [ ] All tests pass
- [ ] Documentation updated
- [ ] Type hints added
- [ ] Commit messages follow convention
- [ ] PR description explains changes

### PR Template

```markdown
## Description
Brief description of changes.

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests pass locally
```

## 🏆 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Project documentation

## 📞 Getting Help

- **GitHub Discussions**: For questions and discussions
- **GitHub Issues**: For bug reports and feature requests
- **Discord**: [Join our Discord server](https://discord.gg/ai-terminal)

Thank you for contributing to AI Terminal! 🚀
