"""
Approval manager for AI Terminal.

Manages user approval workflows for potentially dangerous operations
with comprehensive logging and audit trails.
"""

import asyncio
import time
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional

from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin


class ApprovalRequest:
    """Represents a single approval request."""
    
    def __init__(
        self,
        operation: str,
        description: str,
        context: Dict[str, Any],
        risk_level: str = "medium",
        timeout: int = 300,  # 5 minutes default
    ):
        """Initialize approval request."""
        self.id = f"approval_{int(time.time() * 1000)}"
        self.operation = operation
        self.description = description
        self.context = context
        self.risk_level = risk_level
        self.timeout = timeout
        
        self.created_at = datetime.now()
        self.status = "pending"  # pending, approved, denied, expired
        self.response_time: Optional[datetime] = None
        self.user_response: Optional[str] = None
        
        # Event for async waiting
        self.response_event = asyncio.Event()
    
    def approve(self, user_response: str = "") -> None:
        """Approve the request."""
        self.status = "approved"
        self.response_time = datetime.now()
        self.user_response = user_response
        self.response_event.set()
    
    def deny(self, user_response: str = "") -> None:
        """Deny the request."""
        self.status = "denied"
        self.response_time = datetime.now()
        self.user_response = user_response
        self.response_event.set()
    
    def expire(self) -> None:
        """Mark request as expired."""
        self.status = "expired"
        self.response_time = datetime.now()
        self.response_event.set()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.id,
            "operation": self.operation,
            "description": self.description,
            "context": self.context,
            "risk_level": self.risk_level,
            "timeout": self.timeout,
            "created_at": self.created_at.isoformat(),
            "status": self.status,
            "response_time": self.response_time.isoformat() if self.response_time else None,
            "user_response": self.user_response,
        }


class ApprovalManager(LoggerMixin):
    """
    Manages approval workflows for AI Terminal operations.
    
    Handles user approval requests, maintains audit logs,
    and provides callbacks for UI integration.
    """
    
    def __init__(self, config: Config):
        """Initialize approval manager."""
        self.config = config
        self.security_config = config.get_security_config()
        
        # Active approval requests
        self.pending_requests: Dict[str, ApprovalRequest] = {}
        self.completed_requests: List[ApprovalRequest] = []
        
        # Callbacks for UI integration
        self.on_approval_required: Optional[Callable] = None
        self.on_approval_completed: Optional[Callable] = None
        
        # Statistics
        self.stats = {
            "total_requests": 0,
            "approved": 0,
            "denied": 0,
            "expired": 0,
        }
        
        self.logger.info("Approval manager initialized")
    
    async def request_approval(
        self,
        operation: str,
        description: str,
        context: Dict[str, Any],
        risk_level: str = "medium",
        timeout: int = 300,
    ) -> bool:
        """
        Request user approval for an operation.
        
        Args:
            operation: Type of operation requiring approval
            description: Human-readable description
            context: Operation context and parameters
            risk_level: Risk level (low, medium, high)
            timeout: Timeout in seconds
            
        Returns:
            True if approved, False if denied or expired
        """
        # Check if approval is disabled
        if not self.security_config.require_approval:
            self.logger.debug(f"Approval disabled, auto-approving: {operation}")
            return True
        
        # Create approval request
        request = ApprovalRequest(
            operation=operation,
            description=description,
            context=context,
            risk_level=risk_level,
            timeout=timeout,
        )
        
        self.pending_requests[request.id] = request
        self.stats["total_requests"] += 1
        
        self.logger.info(f"Approval requested: {request.id} - {operation}")
        
        try:
            # Notify UI if callback is set
            if self.on_approval_required:
                await self.on_approval_required(request)
            
            # Wait for response or timeout
            try:
                await asyncio.wait_for(request.response_event.wait(), timeout=timeout)
            except asyncio.TimeoutError:
                request.expire()
                self.logger.warning(f"Approval request expired: {request.id}")
            
            # Update statistics
            if request.status == "approved":
                self.stats["approved"] += 1
                result = True
            elif request.status == "denied":
                self.stats["denied"] += 1
                result = False
            else:  # expired
                self.stats["expired"] += 1
                result = False
            
            # Move to completed requests
            del self.pending_requests[request.id]
            self.completed_requests.append(request)
            
            # Limit completed requests history
            if len(self.completed_requests) > 1000:
                self.completed_requests = self.completed_requests[-500:]
            
            # Notify completion
            if self.on_approval_completed:
                await self.on_approval_completed(request, result)
            
            self.logger.info(f"Approval {request.status}: {request.id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Error in approval request {request.id}: {e}")
            # Clean up on error
            if request.id in self.pending_requests:
                del self.pending_requests[request.id]
            return False
    
    def approve_request(self, request_id: str, user_response: str = "") -> bool:
        """
        Approve a pending request.
        
        Args:
            request_id: ID of the request to approve
            user_response: Optional user response/comment
            
        Returns:
            True if request was found and approved
        """
        if request_id in self.pending_requests:
            request = self.pending_requests[request_id]
            request.approve(user_response)
            self.logger.info(f"Request approved by user: {request_id}")
            return True
        
        self.logger.warning(f"Approval request not found: {request_id}")
        return False
    
    def deny_request(self, request_id: str, user_response: str = "") -> bool:
        """
        Deny a pending request.
        
        Args:
            request_id: ID of the request to deny
            user_response: Optional user response/comment
            
        Returns:
            True if request was found and denied
        """
        if request_id in self.pending_requests:
            request = self.pending_requests[request_id]
            request.deny(user_response)
            self.logger.info(f"Request denied by user: {request_id}")
            return True
        
        self.logger.warning(f"Approval request not found: {request_id}")
        return False
    
    def get_pending_requests(self) -> List[ApprovalRequest]:
        """Get all pending approval requests."""
        return list(self.pending_requests.values())
    
    def get_request_history(self, limit: int = 100) -> List[ApprovalRequest]:
        """Get approval request history."""
        return self.completed_requests[-limit:]
    
    def get_request_by_id(self, request_id: str) -> Optional[ApprovalRequest]:
        """Get a specific request by ID."""
        # Check pending requests first
        if request_id in self.pending_requests:
            return self.pending_requests[request_id]
        
        # Check completed requests
        for request in self.completed_requests:
            if request.id == request_id:
                return request
        
        return None
    
    def cancel_all_pending(self) -> int:
        """Cancel all pending requests."""
        count = len(self.pending_requests)
        
        for request in self.pending_requests.values():
            request.deny("Cancelled by system")
        
        self.pending_requests.clear()
        self.logger.info(f"Cancelled {count} pending approval requests")
        
        return count
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get approval statistics."""
        return {
            **self.stats,
            "pending_count": len(self.pending_requests),
            "completed_count": len(self.completed_requests),
            "approval_rate": (
                self.stats["approved"] / max(1, self.stats["total_requests"]) * 100
                if self.stats["total_requests"] > 0 else 0
            ),
        }
    
    def export_audit_log(self) -> List[Dict[str, Any]]:
        """Export complete audit log of all requests."""
        audit_log = []
        
        # Add completed requests
        for request in self.completed_requests:
            audit_log.append(request.to_dict())
        
        # Add pending requests
        for request in self.pending_requests.values():
            audit_log.append(request.to_dict())
        
        # Sort by creation time
        audit_log.sort(key=lambda x: x["created_at"])
        
        return audit_log
    
    async def cleanup_expired_requests(self) -> int:
        """Clean up expired requests."""
        expired_count = 0
        current_time = datetime.now()
        
        expired_ids = []
        for request_id, request in self.pending_requests.items():
            elapsed = (current_time - request.created_at).total_seconds()
            if elapsed > request.timeout:
                request.expire()
                expired_ids.append(request_id)
                expired_count += 1
        
        # Move expired requests to completed
        for request_id in expired_ids:
            request = self.pending_requests.pop(request_id)
            self.completed_requests.append(request)
            self.stats["expired"] += 1
        
        if expired_count > 0:
            self.logger.info(f"Cleaned up {expired_count} expired approval requests")
        
        return expired_count
