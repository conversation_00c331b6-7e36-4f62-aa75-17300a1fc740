"""
Ollama provider implementation for AI Terminal.

Provides integration with local Ollama models for privacy-focused
AI interactions without requiring external API keys.
"""

import json
from typing import Any, Dict, List, Optional

import aiohttp

from ai_terminal.providers.base import BaseProvider, ProviderError


class OllamaProvider(BaseProvider):
    """Ollama local AI provider implementation."""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """Initialize Ollama provider."""
        super().__init__(name, config)
        self.base_url = config.get("base_url", "http://localhost:11434")
        self.session: Optional[aiohttp.ClientSession] = None
        self.default_model = config.get("model", "llama2")
    
    async def initialize(self) -> None:
        """Initialize the Ollama provider."""
        try:
            # Create HTTP session (no API key needed for Ollama)
            timeout = aiohttp.ClientTimeout(total=60)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            await self.validate_connection()
            self.is_initialized = True
            self.logger.info("Ollama provider initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Ollama provider: {e}")
            raise
    
    async def generate_response(
        self,
        messages: List[Dict[str, Any]],
        model: Optional[str] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """Generate response from Ollama API."""
        if not self.session:
            raise ProviderError("Provider not initialized", self.name)
        
        model = model or self.default_model
        
        # Convert messages to Ollama format
        prompt = self._messages_to_prompt(messages)
        
        payload = {
            "model": model,
            "prompt": prompt,
            "stream": stream,
            "options": {
                "temperature": kwargs.get("temperature", self.temperature),
                "num_predict": kwargs.get("max_tokens", self.max_tokens),
            }
        }
        
        try:
            if stream:
                return await self._generate_streaming_response(payload)
            else:
                return await self._generate_single_response(payload)
                
        except Exception as e:
            self.logger.error(f"Ollama API error: {e}")
            raise ProviderError(f"API request failed: {e}", self.name)
    
    def _messages_to_prompt(self, messages: List[Dict[str, Any]]) -> str:
        """Convert messages to a single prompt for Ollama."""
        prompt_parts = []
        
        for message in messages:
            role = message["role"]
            content = message["content"]
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"Human: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
        
        prompt_parts.append("Assistant:")
        return "\n\n".join(prompt_parts)
    
    async def _generate_single_response(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a single response from Ollama."""
        url = f"{self.base_url}/api/generate"
        
        async with self.session.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise ProviderError(f"Ollama error {response.status}: {error_text}", self.name)
            
            data = await response.json()
            return self.parse_response(data)
    
    async def _generate_streaming_response(self, payload: Dict[str, Any]):
        """Generate a streaming response from Ollama."""
        url = f"{self.base_url}/api/generate"
        
        class OllamaStreamingResponse:
            def __init__(self, provider, session, url, payload):
                self.provider = provider
                self.session = session
                self.url = url
                self.payload = payload
            
            async def __aiter__(self):
                async with self.session.post(self.url, json=self.payload) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise ProviderError(f"Ollama streaming error {response.status}: {error_text}", self.provider.name)
                    
                    async for line in response.content:
                        try:
                            chunk_data = json.loads(line.decode('utf-8'))
                            yield self.provider.parse_streaming_chunk(chunk_data)
                            
                            if chunk_data.get("done", False):
                                break
                        except json.JSONDecodeError:
                            continue
            
            def stream(self):
                return self.__aiter__()
        
        return OllamaStreamingResponse(self, self.session, url, payload)
    
    def parse_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Ollama API response."""
        return {
            "content": response.get("response", ""),
            "tool_calls": [],  # Ollama doesn't support function calling yet
            "usage": {
                "prompt_tokens": response.get("prompt_eval_count", 0),
                "completion_tokens": response.get("eval_count", 0),
                "total_tokens": response.get("prompt_eval_count", 0) + response.get("eval_count", 0),
            },
            "model": response.get("model", self.default_model),
            "provider": self.name,
            "finish_reason": "stop" if response.get("done", False) else None,
        }
    
    def parse_streaming_chunk(self, chunk: Dict[str, Any]) -> Dict[str, Any]:
        """Parse Ollama streaming chunk."""
        return {
            "content": chunk.get("response", ""),
            "tool_calls": [],
            "finish_reason": "stop" if chunk.get("done", False) else None,
            "model": chunk.get("model", self.default_model),
            "provider": self.name,
        }
    
    async def validate_connection(self) -> bool:
        """Validate connection to Ollama."""
        try:
            if not self.session:
                return False
            
            url = f"{self.base_url}/api/tags"
            async with self.session.get(url) as response:
                return response.status == 200
                
        except Exception as e:
            self.logger.error(f"Ollama connection validation failed: {e}")
            return False
    
    async def list_models(self) -> List[str]:
        """List available Ollama models."""
        try:
            if not self.session:
                return []
            
            url = f"{self.base_url}/api/tags"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return [model["name"] for model in data.get("models", [])]
                else:
                    return []
                    
        except Exception as e:
            self.logger.error(f"Failed to list Ollama models: {e}")
            return []
    
    async def get_model_info(self, model: str) -> Dict[str, Any]:
        """Get information about an Ollama model."""
        try:
            if not self.session:
                return {"name": model}
            
            url = f"{self.base_url}/api/show"
            payload = {"name": model}
            
            async with self.session.post(url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "name": model,
                        "description": data.get("details", {}).get("family", ""),
                        "context_window": 4096,  # Default for most Ollama models
                        "supports_function_calling": False,
                        "supports_streaming": True,
                    }
                else:
                    return {"name": model, "context_window": 4096}
                    
        except Exception as e:
            self.logger.error(f"Failed to get Ollama model info: {e}")
            return {"name": model, "context_window": 4096}
    
    def supports_function_calling(self) -> bool:
        """Ollama doesn't support function calling yet."""
        return False
    
    async def shutdown(self) -> None:
        """Shutdown the Ollama provider."""
        if self.session:
            await self.session.close()
            self.session = None
        await super().shutdown()
