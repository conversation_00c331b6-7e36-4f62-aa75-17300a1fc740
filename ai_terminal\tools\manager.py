"""
Tool manager for AI Terminal.

Manages the registration, execution, and lifecycle of all tools
with support for security validation and performance monitoring.
"""

import asyncio
import time
from typing import Any, Dict, List, Optional, Type

from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin
from ai_terminal.tools.base import <PERSON>Tool, ToolError, ToolExecutionError, ToolTimeoutError
from ai_terminal.tools.shell import ShellTool
from ai_terminal.tools.file_ops import FileOperationsTool
from ai_terminal.tools.git import GitTool
from ai_terminal.tools.code_assist import CodeAssistTool
from ai_terminal.tools.package_manager import PackageManagerTool
from ai_terminal.tools.system_monitor import SystemMonitorTool


class ToolManager(LoggerMixin):
    """
    Tool Manager for AI Terminal.
    
    Manages tool registration, execution, security validation,
    and performance monitoring for all available tools.
    """
    
    # Registry of available tool classes
    TOOL_CLASSES: Dict[str, Type[BaseTool]] = {
        "shell": ShellTool,
        "file_ops": FileOperationsTool,
        "git": GitTool,
        "code_assist": CodeAssistTool,
        "package_manager": PackageManagerTool,
        "system_monitor": SystemMonitorTool,
    }
    
    def __init__(self, config: Config):
        """Initialize tool manager."""
        self.config = config
        self.tools: Dict[str, BaseTool] = {}
        self.enabled_tools = set(config.get("tools.enabled", []))
        self.tool_timeout = config.get("tools.shell_timeout", 30)
        self.max_file_size = config.get("tools.max_file_size", 10485760)  # 10MB
        
        # Performance tracking
        self.execution_stats: Dict[str, Dict[str, Any]] = {}
    
    async def initialize(self) -> None:
        """Initialize all enabled tools."""
        try:
            for tool_name in self.enabled_tools:
                if tool_name in self.TOOL_CLASSES:
                    await self._register_tool(tool_name)
                else:
                    self.logger.warning(f"Unknown tool: {tool_name}")
            
            self.logger.info(f"Tool manager initialized with {len(self.tools)} tools")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize tool manager: {e}")
            raise
    
    async def _register_tool(self, tool_name: str) -> None:
        """Register a single tool."""
        try:
            tool_class = self.TOOL_CLASSES[tool_name]
            tool_instance = tool_class()
            
            # Configure tool based on settings
            tool_instance.timeout = self.tool_timeout
            
            self.tools[tool_name] = tool_instance
            self.execution_stats[tool_name] = {
                "total_executions": 0,
                "successful_executions": 0,
                "failed_executions": 0,
                "total_execution_time": 0.0,
                "average_execution_time": 0.0,
            }
            
            self.logger.debug(f"Registered tool: {tool_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to register tool {tool_name}: {e}")
    
    async def execute_tool(self, tool_name: str, args: Dict[str, Any]) -> Any:
        """
        Execute a tool with the given arguments.
        
        Args:
            tool_name: Name of the tool to execute
            args: Arguments for the tool
            
        Returns:
            Tool execution result
        """
        if tool_name not in self.tools:
            raise ToolError(f"Tool '{tool_name}' not found", tool_name)
        
        tool = self.tools[tool_name]
        start_time = time.time()
        
        try:
            # Pre-execution validation
            if not await tool.pre_execute(args):
                raise ToolExecutionError("Pre-execution validation failed", tool_name)
            
            # Execute with timeout
            try:
                result = await asyncio.wait_for(
                    tool.execute(**args),
                    timeout=tool.timeout
                )
            except asyncio.TimeoutError:
                raise ToolTimeoutError(f"Tool execution timed out after {tool.timeout}s", tool_name)
            
            # Post-execution processing
            result = await tool.post_execute(args, result)
            
            # Update statistics
            execution_time = time.time() - start_time
            await self._update_stats(tool_name, True, execution_time)
            
            self.logger.debug(f"Tool {tool_name} executed successfully in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            await self._update_stats(tool_name, False, execution_time)
            
            if isinstance(e, ToolError):
                raise
            else:
                raise ToolExecutionError(f"Tool execution failed: {e}", tool_name)
    
    async def _update_stats(self, tool_name: str, success: bool, execution_time: float) -> None:
        """Update execution statistics for a tool."""
        if tool_name not in self.execution_stats:
            return
        
        stats = self.execution_stats[tool_name]
        stats["total_executions"] += 1
        stats["total_execution_time"] += execution_time
        
        if success:
            stats["successful_executions"] += 1
        else:
            stats["failed_executions"] += 1
        
        # Update average execution time
        if stats["total_executions"] > 0:
            stats["average_execution_time"] = stats["total_execution_time"] / stats["total_executions"]
    
    async def get_available_tools(self) -> List[BaseTool]:
        """Get list of available tools."""
        return list(self.tools.values())
    
    async def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """Get a specific tool by name."""
        return self.tools.get(tool_name)
    
    async def get_tool_schemas(self) -> List[Dict[str, Any]]:
        """Get schemas for all available tools."""
        schemas = []
        for tool in self.tools.values():
            if tool.enabled:
                schemas.append(tool.get_schema())
        return schemas
    
    async def enable_tool(self, tool_name: str) -> bool:
        """Enable a tool."""
        if tool_name in self.tools:
            self.tools[tool_name].enabled = True
            self.logger.info(f"Enabled tool: {tool_name}")
            return True
        return False
    
    async def disable_tool(self, tool_name: str) -> bool:
        """Disable a tool."""
        if tool_name in self.tools:
            self.tools[tool_name].enabled = False
            self.logger.info(f"Disabled tool: {tool_name}")
            return True
        return False
    
    async def add_tool(self, tool_instance: BaseTool) -> bool:
        """Add a custom tool instance."""
        try:
            self.tools[tool_instance.name] = tool_instance
            self.execution_stats[tool_instance.name] = {
                "total_executions": 0,
                "successful_executions": 0,
                "failed_executions": 0,
                "total_execution_time": 0.0,
                "average_execution_time": 0.0,
            }
            
            self.logger.info(f"Added custom tool: {tool_instance.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add tool {tool_instance.name}: {e}")
            return False
    
    async def remove_tool(self, tool_name: str) -> bool:
        """Remove a tool."""
        if tool_name in self.tools:
            del self.tools[tool_name]
            if tool_name in self.execution_stats:
                del self.execution_stats[tool_name]
            
            self.logger.info(f"Removed tool: {tool_name}")
            return True
        return False
    
    async def get_tool_help(self, tool_name: Optional[str] = None) -> str:
        """Get help text for tools."""
        if tool_name:
            if tool_name in self.tools:
                return self.tools[tool_name].get_help()
            else:
                return f"Tool '{tool_name}' not found."
        else:
            # Return help for all tools
            help_text = "# Available Tools\n\n"
            for tool in self.tools.values():
                if tool.enabled:
                    help_text += tool.get_help() + "\n\n"
            return help_text
    
    async def get_tool_stats(self) -> Dict[str, Any]:
        """Get tool execution statistics."""
        return {
            "total_tools": len(self.tools),
            "enabled_tools": len([t for t in self.tools.values() if t.enabled]),
            "tool_stats": self.execution_stats.copy(),
            "tool_status": {
                name: {
                    "enabled": tool.enabled,
                    "requires_approval": tool.requires_approval,
                    "timeout": tool.timeout,
                }
                for name, tool in self.tools.items()
            }
        }
    
    async def validate_tool_security(self, tool_name: str, args: Dict[str, Any]) -> Dict[str, Any]:
        """Validate tool execution for security concerns."""
        if tool_name not in self.tools:
            return {"safe": False, "reason": "Tool not found"}
        
        tool = self.tools[tool_name]
        
        # Check if tool is enabled
        if not tool.enabled:
            return {"safe": False, "reason": "Tool is disabled"}
        
        # Check if tool requires approval
        if tool.is_dangerous(args):
            return {
                "safe": False,
                "reason": "Tool execution requires approval",
                "requires_approval": True
            }
        
        # Additional security checks based on tool type
        security_config = self.config.get_security_config()
        
        if tool_name == "shell" and "command" in args:
            command = args["command"].lower()
            dangerous_commands = security_config.dangerous_commands
            
            for dangerous_cmd in dangerous_commands:
                if dangerous_cmd in command:
                    return {
                        "safe": False,
                        "reason": f"Command contains dangerous operation: {dangerous_cmd}",
                        "requires_approval": True
                    }
        
        if tool_name == "file_ops" and "path" in args:
            # Check file extension restrictions
            path = args["path"]
            allowed_extensions = security_config.allowed_file_extensions
            
            if allowed_extensions and not any(path.endswith(ext) for ext in allowed_extensions):
                return {
                    "safe": False,
                    "reason": "File extension not allowed",
                    "requires_approval": True
                }
        
        return {"safe": True, "reason": "Tool execution is safe"}
    
    async def test_tool(self, tool_name: str) -> Dict[str, Any]:
        """Test a tool to ensure it's working correctly."""
        if tool_name not in self.tools:
            return {"success": False, "error": "Tool not found"}
        
        tool = self.tools[tool_name]
        
        try:
            # Basic validation
            if not tool.enabled:
                return {"success": False, "error": "Tool is disabled"}
            
            # Get schema to ensure it's valid
            schema = tool.get_schema()
            if not schema:
                return {"success": False, "error": "Invalid tool schema"}
            
            return {
                "success": True,
                "tool_name": tool_name,
                "enabled": tool.enabled,
                "schema_valid": bool(schema),
                "requires_approval": tool.requires_approval,
                "timeout": tool.timeout,
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def shutdown(self) -> None:
        """Shutdown the tool manager."""
        try:
            # Cleanup any tool resources
            for tool in self.tools.values():
                if hasattr(tool, 'cleanup'):
                    try:
                        await tool.cleanup()
                    except Exception as e:
                        self.logger.error(f"Error cleaning up tool {tool.name}: {e}")
            
            self.tools.clear()
            self.execution_stats.clear()
            
            self.logger.info("Tool manager shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during tool manager shutdown: {e}")
