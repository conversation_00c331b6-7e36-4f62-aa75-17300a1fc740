# AI Terminal 🤖

A sophisticated autonomous AI-powered CLI terminal application with modern architecture, advanced features, and enterprise-grade quality.

## ✨ Features

### 🎯 Core Capabilities
- **Autonomous AI Agent**: Intelligent decision-making with context awareness
- **Multi-Provider Support**: DeepSeek, OpenAI, Anthropic, Ollama, Google Gemini
- **Rich Terminal UI**: Modern interface built with Textual and Rich
- **Comprehensive Tool System**: 15+ integrated tools for development workflows
- **Enterprise Security**: Approval workflows, command validation, secure storage

### 🛠️ Tool Ecosystem
- **Shell Operations**: Safe command execution with approval workflows
- **File Management**: Read, write, create, delete with backup mechanisms
- **Git Integration**: Full version control operations
- **Code Assistance**: Formatting, linting, analysis, debugging
- **Package Management**: pip, npm, cargo, apt, brew support
- **System Monitoring**: Resource usage, process management

### 🎨 User Interface Components
- **Terminal Chat**: Conversation display with syntax highlighting
- **Multi-line Editor**: Advanced code input with smart indentation
- **Interactive Overlays**: Help, model selection, session management
- **Real-time Streaming**: Live AI response display
- **Command Completions**: Intelligent auto-suggestions
- **Session Management**: Persistent conversation history

### 🔒 Security & Safety
- **Command Approval**: Review dangerous operations before execution
- **Input Validation**: Comprehensive security checks
- **Encrypted Storage**: Secure API key and session storage
- **Sandboxed Execution**: Isolated tool execution environment
- **Audit Logging**: Complete operation tracking

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/ai-terminal/ai-terminal.git
cd ai-terminal

# Install dependencies
pip install -e .

# Run the application
ai-terminal
```

### First-Time Setup

1. **Launch AI Terminal**:
   ```bash
   ai-terminal
   ```

2. **Follow the onboarding wizard** to configure your AI provider:
   - Choose from DeepSeek, OpenAI, Anthropic, Ollama, or Gemini
   - Enter your API key (stored securely)
   - Select your preferred model
   - Configure basic preferences

3. **Start chatting** with your AI assistant!

## 📋 Requirements

- **Python**: 3.9 or higher
- **Operating System**: Windows 11 WSL, macOS, Linux
- **Terminal**: Modern terminal with Unicode support
- **API Key**: For cloud AI providers (DeepSeek, OpenAI, etc.)

## 💬 Slash Commands

AI Terminal includes a comprehensive slash command system for powerful terminal operations:

### **Session Management**
- `/sessions list` - List all available sessions
- `/sessions new [name]` - Create a new conversation session
- `/sessions load <name>` - Load an existing session
- `/sessions delete <name>` - Delete a session (with confirmation)

### **Conversation Control**
- `/clear` - Clear current chat history (requires confirmation)
- `/history [limit] [search]` - Browse conversation history with optional search
- `/compact` - Compress conversation history to save context
- `/reset` - Reset current session (requires confirmation)

### **AI Model Management**
- `/model` - Show current model and provider
- `/model --provider <provider> --model <model>` - Switch AI model/provider
- `/status` - Show detailed system status and statistics

### **Data Operations**
- `/export [--format json|markdown|text] [--file path]` - Export conversations
- `/import <file> [--format json]` - Import conversation data
- `/diff <file1> [file2]` - Show file differences with syntax highlighting

### **Configuration & Help**
- `/config` - Show all configuration settings
- `/config <key>` - Get specific configuration value
- `/config <key> <value>` - Set configuration value
- `/help [command]` - Show help information
- `/quit` - Exit application (with confirmation)

### **Command Features**
- **Auto-completion**: Tab completion for commands and arguments
- **Argument parsing**: Support for both positional and keyword arguments
- **Confirmation dialogs**: Safety prompts for destructive operations
- **Error handling**: Helpful error messages and suggestions
- **Aliases**: Short forms like `/h` for `/help`, `/cls` for `/clear`

## 🛠️ Enhanced Tools

### **Git Integration**
Complete Git workflow support with enhanced operations:

```bash
# Branch management
git branch                    # List all branches
git branch feature-auth       # Create new branch
git checkout feature-auth     # Switch to branch
git checkout -b new-feature   # Create and switch

# History and changes
git log --limit 10           # View commit history
git diff [file]              # Show changes
git status                   # Repository status

# Repository operations
git clone <url> [directory]  # Clone repository
git add <files>              # Stage changes
git commit -m "message"      # Commit changes
git push                     # Push to remote
```

### **Package Manager Support**
Unified interface for multiple package managers:

**NPM (Node.js)**
```bash
npm install express          # Install package
npm install -g typescript    # Global installation
npm uninstall lodash        # Remove package
npm list                    # List installed packages
npm search react            # Search packages
npm update                  # Update packages
```

**Cargo (Rust)**
```bash
cargo install ripgrep       # Install binary crate
cargo search tokio          # Search crates
cargo uninstall exa         # Remove installed crate
```

**APT (Ubuntu/Debian)**
```bash
apt install curl            # Install package
apt remove vim              # Remove package
apt update                  # Update package lists
apt upgrade                 # Upgrade packages
apt search python           # Search packages
```

**Homebrew (macOS)**
```bash
brew install wget           # Install package
brew install --cask firefox # Install application
brew uninstall node        # Remove package
brew update                 # Update Homebrew
brew search git             # Search packages
```

### **Advanced File Operations**
Enhanced file management with safety features:

- **Atomic Operations**: All file operations are atomic with rollback capability
- **Backup Creation**: Automatic backups before destructive operations
- **Diff Visualization**: Side-by-side file comparison with syntax highlighting
- **Batch Operations**: Process multiple files efficiently
- **Permission Handling**: Proper file permission management
- **Error Recovery**: Graceful handling of file system errors

### **Code Assistance Tools**
Comprehensive development support:

- **Syntax Highlighting**: Support for 100+ programming languages
- **Code Formatting**: Integration with Black, Prettier, rustfmt, and more
- **Linting**: ESLint, flake8, clippy integration
- **Language Servers**: LSP support for intelligent code analysis
- **Refactoring**: Safe code transformations and improvements

## 🔧 Configuration

### AI Providers

Configure multiple AI providers in `~/.config/ai-terminal/config.toml`:

```toml
[ai]
default_provider = "deepseek"
default_model = "deepseek-chat"

[ai.providers.deepseek]
model = "deepseek-chat"
base_url = "https://api.deepseek.com"
enabled = true

[ai.providers.openai]
model = "gpt-4"
base_url = "https://api.openai.com/v1"
enabled = true
```

### Security Settings

```toml
[security]
encrypt_storage = true
require_approval = true
dangerous_commands = ["rm", "del", "format", "shutdown"]
```

### UI Preferences

```toml
[ui]
theme = "dark"
show_timestamps = true
auto_scroll = true
syntax_highlighting = true
animation_speed = 1.0
```

## 💬 Usage Examples

### Basic Interaction
```
> Hello! Can you help me analyze this Python file?

🤖 I'd be happy to help you analyze a Python file! I can:
- Read and examine the code structure
- Check for potential issues or improvements
- Suggest optimizations
- Format the code
- Run static analysis

Which file would you like me to analyze?
```

### File Operations
```
> Read the contents of main.py and suggest improvements

🤖 I'll read the file and analyze it for you.

🔧 Tool Call: file_ops
- operation: read
- path: main.py

[File contents displayed with syntax highlighting]

Based on my analysis, here are some suggestions:
1. Add type hints for better code clarity
2. Consider using pathlib instead of os.path
3. Add docstrings to functions
...
```

### Shell Commands
```
> Run the tests for this project

🤖 I'll run the tests for you. Let me check what testing framework you're using first.

🔧 Tool Call: file_ops
- operation: exists
- path: pytest.ini

🔧 Tool Call: shell
- command: python -m pytest tests/ -v

[Test results displayed]

All tests passed! Your code is working correctly.
```

## 🎮 Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+C` | Quit application |
| `Ctrl+N` | New session |
| `Ctrl+O` | Open session |
| `Ctrl+S` | Save session |
| `Ctrl+H` | Show help |
| `Ctrl+M` | Toggle multi-line mode |
| `F1-F5` | Quick access to overlays |
| `↑/↓` | Navigate input history |
| `Tab` | Show completions |
| `Escape` | Close overlay |

## 🔧 Slash Commands

| Command | Description |
|---------|-------------|
| `/clear` | Clear current conversation |
| `/history` | Browse conversation history |
| `/sessions` | Manage chat sessions |
| `/model` | Switch between AI models |
| `/diff` | Show file differences |
| `/help` | Show help information |
| `/config` | Configuration management |
| `/export` | Export conversations |
| `/quit` | Exit application |

## 🏗️ Architecture

### Core Components

```
ai_terminal/
├── core/           # Application orchestration
├── ui/             # Terminal interface components
├── agents/         # Autonomous agent engine
├── tools/          # Tool system and integrations
├── storage/        # Data persistence and sessions
├── providers/      # Multi-provider LLM integrations
└── security/       # Security and validation
```

### Key Design Principles

- **Modular Architecture**: Clean separation of concerns
- **Async/Await**: Non-blocking operations throughout
- **Type Safety**: Comprehensive type hints
- **Error Handling**: Graceful degradation and recovery
- **Extensibility**: Plugin architecture for custom tools
- **Security First**: Validation and approval workflows

## 🧪 Development

### Setup Development Environment

```bash
# Clone and install in development mode
git clone https://github.com/ai-terminal/ai-terminal.git
cd ai-terminal
pip install -e ".[dev]"

# Run tests
pytest

# Run with debug logging
ai-terminal --debug
```

### Adding Custom Tools

```python
from ai_terminal.tools.base import BaseTool

class MyCustomTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="my_tool",
            description="My custom tool description"
        )
    
    async def execute(self, **kwargs):
        # Tool implementation
        return {"result": "success"}
    
    def get_schema(self):
        return {
            "name": "my_tool",
            "description": "My custom tool",
            "parameters": {
                "type": "object",
                "properties": {
                    "param": {"type": "string"}
                },
                "required": ["param"]
            }
        }
```

## 📊 Performance

- **Startup Time**: < 2 seconds
- **Response Time**: < 500ms for local operations
- **Memory Usage**: < 100MB typical
- **Concurrent Operations**: Full async support
- **Database**: SQLite with WAL mode for performance

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Textual**: Amazing terminal UI framework
- **Rich**: Beautiful terminal formatting
- **AI Providers**: DeepSeek, OpenAI, Anthropic, Google, Ollama
- **Python Community**: Excellent ecosystem and libraries

## 📞 Support

- **Documentation**: [docs.ai-terminal.dev](https://docs.ai-terminal.dev)
- **Issues**: [GitHub Issues](https://github.com/ai-terminal/ai-terminal/issues)
- **Discussions**: [GitHub Discussions](https://github.com/ai-terminal/ai-terminal/discussions)
- **Email**: <EMAIL>

---

**AI Terminal** - Bringing the future of AI assistance to your terminal. 🚀
