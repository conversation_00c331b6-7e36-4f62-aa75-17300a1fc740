"""
Multi-line editor component for AI Terminal.

Provides advanced text editing capabilities with syntax highlighting,
code formatting, and intelligent indentation for code input.
"""

from typing import Optional

from rich.syntax import Syntax
from rich.text import Text
from textual import on
from textual.events import Key
from textual.widgets import TextArea

from ai_terminal.core.config import Config
from ai_terminal.core.logger import LoggerMixin


class MultilineEditor(TextArea, LoggerMixin):
    """
    Advanced multi-line text editor with syntax highlighting.
    
    Features:
    - Syntax highlighting for multiple languages
    - Smart indentation
    - Code formatting
    - Line numbers
    - Bracket matching
    - Auto-completion
    """
    
    DEFAULT_CSS = """
    MultilineEditor {
        width: 100%;
        height: auto;
        min-height: 5;
        max-height: 15;
        border: solid $accent;
        border-title-align: left;
        background: $surface;
        color: $text;
    }
    
    .editor-content {
        padding: 1;
        background: $surface;
    }
    
    .line-numbers {
        width: 4;
        background: $panel;
        color: $text-muted;
        text-align: right;
        padding-right: 1;
    }
    
    .syntax-highlight {
        background: $surface;
    }
    """
    
    def __init__(self, config: Config, **kwargs):
        """Initialize the multi-line editor."""
        super().__init__(**kwargs)
        
        self.config = config
        self.syntax_highlighting = config.get("ui.syntax_highlighting", True)
        self.auto_indent = True
        self.show_line_numbers = True
        self.current_language = "text"
        
        # Editor state
        self.indent_size = 4
        self.use_spaces = True
        self.bracket_pairs = {
            "(": ")",
            "[": "]",
            "{": "}",
            '"': '"',
            "'": "'",
        }
        
        self.border_title = "📝 Multi-line Editor"
    
    def on_mount(self) -> None:
        """Handle component mount."""
        self.show_line_numbers = True
        if self.syntax_highlighting:
            self._detect_language()
    
    @on(Key)
    async def on_key(self, event: Key) -> None:
        """Handle key events."""
        if event.key == "ctrl+enter":
            await self._submit_content()
            event.prevent_default()
        elif event.key == "tab":
            await self._handle_tab()
            event.prevent_default()
        elif event.key == "shift+tab":
            await self._handle_shift_tab()
            event.prevent_default()
        elif event.key == "enter":
            await self._handle_enter()
            event.prevent_default()
        elif event.key in self.bracket_pairs:
            await self._handle_bracket_open(event.key)
            event.prevent_default()
        elif event.key in self.bracket_pairs.values():
            await self._handle_bracket_close(event.key)
            event.prevent_default()
        elif event.key == "ctrl+/":
            await self._toggle_comment()
            event.prevent_default()
        elif event.key == "ctrl+f":
            await self._format_code()
            event.prevent_default()
    
    async def _submit_content(self) -> None:
        """Submit the editor content."""
        content = self.text.strip()
        if content:
            # TODO: Send content to input processor
            self.logger.debug(f"Submitting content: {len(content)} characters")
            self.clear()
    
    async def _handle_tab(self) -> None:
        """Handle tab key for indentation."""
        if self.use_spaces:
            indent = " " * self.indent_size
        else:
            indent = "\t"
        
        # Insert indentation at cursor
        cursor_pos = self.cursor_position
        current_text = self.text
        new_text = current_text[:cursor_pos] + indent + current_text[cursor_pos:]
        self.text = new_text
        self.cursor_position = cursor_pos + len(indent)
    
    async def _handle_shift_tab(self) -> None:
        """Handle shift+tab for unindentation."""
        cursor_pos = self.cursor_position
        current_text = self.text
        
        # Find start of current line
        line_start = current_text.rfind("\n", 0, cursor_pos) + 1
        line_text = current_text[line_start:cursor_pos]
        
        # Remove indentation
        if line_text.startswith(" " * self.indent_size):
            new_text = (
                current_text[:line_start] +
                line_text[self.indent_size:] +
                current_text[cursor_pos:]
            )
            self.text = new_text
            self.cursor_position = cursor_pos - self.indent_size
        elif line_text.startswith("\t"):
            new_text = (
                current_text[:line_start] +
                line_text[1:] +
                current_text[cursor_pos:]
            )
            self.text = new_text
            self.cursor_position = cursor_pos - 1
    
    async def _handle_enter(self) -> None:
        """Handle enter key with smart indentation."""
        cursor_pos = self.cursor_position
        current_text = self.text
        
        # Find current line indentation
        line_start = current_text.rfind("\n", 0, cursor_pos) + 1
        line_text = current_text[line_start:cursor_pos]
        
        # Calculate indentation
        indent = ""
        for char in line_text:
            if char in " \t":
                indent += char
            else:
                break
        
        # Check if we need extra indentation (after opening brackets)
        if line_text.rstrip().endswith((":", "{", "[", "(")):
            if self.use_spaces:
                indent += " " * self.indent_size
            else:
                indent += "\t"
        
        # Insert newline with indentation
        new_text = current_text[:cursor_pos] + "\n" + indent + current_text[cursor_pos:]
        self.text = new_text
        self.cursor_position = cursor_pos + 1 + len(indent)
    
    async def _handle_bracket_open(self, bracket: str) -> None:
        """Handle opening bracket with auto-closing."""
        cursor_pos = self.cursor_position
        current_text = self.text
        
        closing_bracket = self.bracket_pairs[bracket]
        
        # Insert bracket pair
        new_text = (
            current_text[:cursor_pos] +
            bracket + closing_bracket +
            current_text[cursor_pos:]
        )
        self.text = new_text
        self.cursor_position = cursor_pos + 1
    
    async def _handle_bracket_close(self, bracket: str) -> None:
        """Handle closing bracket with smart skipping."""
        cursor_pos = self.cursor_position
        current_text = self.text
        
        # Check if next character is the same closing bracket
        if (cursor_pos < len(current_text) and 
            current_text[cursor_pos] == bracket):
            # Skip the existing bracket
            self.cursor_position = cursor_pos + 1
        else:
            # Insert the bracket normally
            new_text = current_text[:cursor_pos] + bracket + current_text[cursor_pos:]
            self.text = new_text
            self.cursor_position = cursor_pos + 1
    
    async def _toggle_comment(self) -> None:
        """Toggle comment on current line or selection."""
        # TODO: Implement comment toggling based on detected language
        pass
    
    async def _format_code(self) -> None:
        """Format code using appropriate formatter."""
        try:
            content = self.text
            if not content.strip():
                return
            
            # Detect language and format accordingly
            if self.current_language == "python":
                # TODO: Use black or autopep8 for Python formatting
                pass
            elif self.current_language == "javascript":
                # TODO: Use prettier for JavaScript formatting
                pass
            elif self.current_language == "json":
                import json
                try:
                    parsed = json.loads(content)
                    formatted = json.dumps(parsed, indent=2)
                    self.text = formatted
                except json.JSONDecodeError:
                    pass
            
        except Exception as e:
            self.logger.error(f"Error formatting code: {e}")
    
    def _detect_language(self) -> None:
        """Detect programming language from content."""
        content = self.text.lower()
        
        # Simple language detection based on keywords
        if any(keyword in content for keyword in ["def ", "import ", "class ", "if __name__"]):
            self.current_language = "python"
        elif any(keyword in content for keyword in ["function", "const ", "let ", "var "]):
            self.current_language = "javascript"
        elif content.strip().startswith("{") and content.strip().endswith("}"):
            self.current_language = "json"
        elif any(keyword in content for keyword in ["SELECT", "FROM", "WHERE", "INSERT"]):
            self.current_language = "sql"
        elif content.strip().startswith("```"):
            # Markdown code block
            self.current_language = "markdown"
        else:
            self.current_language = "text"
        
        # Update syntax highlighting
        if self.syntax_highlighting:
            self._apply_syntax_highlighting()
    
    def _apply_syntax_highlighting(self) -> None:
        """Apply syntax highlighting to the content."""
        if not self.syntax_highlighting or self.current_language == "text":
            return
        
        try:
            # TODO: Implement real-time syntax highlighting
            # This would require integration with Pygments and Textual's rich text
            pass
        except Exception as e:
            self.logger.error(f"Error applying syntax highlighting: {e}")
    
    def get_current_line(self) -> str:
        """Get the current line text."""
        cursor_pos = self.cursor_position
        current_text = self.text
        
        line_start = current_text.rfind("\n", 0, cursor_pos) + 1
        line_end = current_text.find("\n", cursor_pos)
        if line_end == -1:
            line_end = len(current_text)
        
        return current_text[line_start:line_end]
    
    def insert_text(self, text: str) -> None:
        """Insert text at cursor position."""
        cursor_pos = self.cursor_position
        current_text = self.text
        
        new_text = current_text[:cursor_pos] + text + current_text[cursor_pos:]
        self.text = new_text
        self.cursor_position = cursor_pos + len(text)
    
    def get_selected_text(self) -> str:
        """Get currently selected text."""
        # TODO: Implement text selection support
        return ""
    
    def replace_selected_text(self, text: str) -> None:
        """Replace selected text with new text."""
        # TODO: Implement text replacement
        pass
