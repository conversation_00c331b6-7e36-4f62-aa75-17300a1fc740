"""
Setup configuration for AI Terminal.

Defines package metadata, dependencies, and installation configuration
for the AI Terminal application.
"""

from pathlib import Path
from setuptools import find_packages, setup

# Read README for long description
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
if requirements_path.exists():
    requirements = requirements_path.read_text(encoding="utf-8").strip().split("\n")
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith("#")]
else:
    requirements = [
        # Core dependencies
        "textual>=0.45.0",
        "rich>=13.0.0",
        "aiofiles>=23.0.0",
        "aiohttp>=3.8.0",
        "sqlalchemy>=2.0.0",
        "aiosqlite>=0.19.0",
        "cryptography>=41.0.0",
        "keyring>=24.0.0",
        "psutil>=5.9.0",
        "chardet>=5.0.0",
        
        # AI Provider dependencies
        "openai>=1.0.0",
        "anthropic>=0.7.0",
        "google-generativeai>=0.3.0",
        
        # Optional dependencies
        "httpx>=0.25.0",
        "pydantic>=2.0.0",
        "toml>=0.10.0",
    ]

# Development dependencies
dev_requirements = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.0.0",
]

setup(
    name="ai-terminal",
    version="1.0.0",
    author="AI Terminal Team",
    author_email="<EMAIL>",
    description="Autonomous AI-powered CLI terminal application with modern architecture",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/ai-terminal/ai-terminal",
    project_urls={
        "Bug Reports": "https://github.com/ai-terminal/ai-terminal/issues",
        "Source": "https://github.com/ai-terminal/ai-terminal",
        "Documentation": "https://docs.ai-terminal.dev",
    },
    packages=find_packages(exclude=["tests", "tests.*"]),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: System Administrators",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: System :: Shells",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Terminals",
        "Environment :: Console",
    ],
    python_requires=">=3.9",
    install_requires=requirements,
    extras_require={
        "dev": dev_requirements,
        "all": requirements + dev_requirements,
    },
    entry_points={
        "console_scripts": [
            "ai-terminal=ai_terminal.__main__:cli_main",
        ],
    },
    include_package_data=True,
    package_data={
        "ai_terminal": [
            "config/*.toml",
            "ui/themes/*.json",
            "data/*.sql",
        ],
    },
    zip_safe=False,
    keywords=[
        "ai", "terminal", "cli", "assistant", "automation", "llm", 
        "chatgpt", "claude", "deepseek", "autonomous", "agent"
    ],
    platforms=["any"],
)
