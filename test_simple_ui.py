#!/usr/bin/env python3
"""Simple test to verify basic UI components work."""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'ai_terminal'))

from textual.app import App
from textual.widgets import Static

class SimpleTestApp(App):
    """Simple test app."""
    
    def compose(self):
        yield Static("Hello, AI Terminal!", id="hello")

def test_simple_ui():
    """Test basic UI functionality."""
    print("Testing simple UI...")
    
    try:
        app = SimpleTestApp()
        print("✅ Simple UI app created successfully!")
        return True
    except Exception as e:
        print(f"❌ Simple UI test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_ui()
    sys.exit(0 if success else 1)
